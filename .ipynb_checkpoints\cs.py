from PyQt6 import Qt<PERSON><PERSON>, QtGui, QtWidgets
class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(499, 322)
        self.centralwidget = QtWidgets.QWidget(parent=MainWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.centralwidget)
        self.verticalLayout.setObjectName("verticalLayout")
        self.pushButton = QtWidgets.QPushButton(parent=self.centralwidget)
        font = QtGui.QFont()
        font.setPointSize(20)
        self.pushButton.setFont(font)
        self.pushButton.setObjectName("pushButton")
        self.verticalLayout.addWidget(self.pushButton)
        self.pushButton_2 = QtWidgets.QPushButton(parent=self.centralwidget)
        font = QtGui.QFont()
        font.setPointSize(20)
        self.pushButton_2.setFont(font)
        self.pushButton_2.setObjectName("pushButton_2")
        self.verticalLayout.addWidget(self.pushButton_2)
        MainWindow.setCentralWidget(self.centralwidget)
        self.statusbar = QtWidgets.QStatusBar(parent=MainWindow)
        self.statusbar.setObjectName("statusbar")
        MainWindow.setStatusBar(self.statusbar)
        self.retranslateUi(MainWindow)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)
    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "岩石力学实验数据分析处理软件"))
        self.pushButton.setText(_translate("MainWindow", "两组实验"))
        self.pushButton_2.setText(_translate("MainWindow", "多组实验"))
import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QFileDialog, QGraphicsPixmapItem, QGraphicsScene
from main import *
from Single_calculation_RUN import single_calculationWindow
from Multiple_computation_RUN import Multiple_computationWindow
class MyMainWindow(QMainWindow, Ui_MainWindow):
    def __init__(self, parent=None):
        super(MyMainWindow, self).__init__(parent)
        self.path = ''
        self.setupUi(self)
        self.pushButton.clicked.connect(self.open_single_calculation_window)
        self.pushButton_2.clicked.connect(self.open_Multiple_computation_window)
    def open_single_calculation_window(self):
        self.encode_window = single_calculationWindow()
        self.encode_window.show()
    def open_Multiple_computation_window(self):
        self.encode_window = Multiple_computationWindow()
        self.encode_window.show()
if __name__ == "__main__":
    app = QApplication(sys.argv)
    my_win = MyMainWindow()
    my_win.show()
    sys.exit(app.exec())
from PyQt6 import QtCore, QtGui, QtWidgets
class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(997, 556)
        self.groupBox_2 = QtWidgets.QGroupBox(parent=Form)
        self.groupBox_2.setGeometry(QtCore.QRect(10, 30, 421, 501))
        font = QtGui.QFont()
        font.setPointSize(20)
        self.groupBox_2.setFont(font)
        self.groupBox_2.setObjectName("groupBox_2")
        self.groupBox = QtWidgets.QGroupBox(parent=self.groupBox_2)
        self.groupBox.setGeometry(QtCore.QRect(30, 50, 361, 191))
        font = QtGui.QFont()
        font.setPointSize(18)
        self.groupBox.setFont(font)
        self.groupBox.setObjectName("groupBox")
        self.lineEdit_15 = QtWidgets.QLineEdit(parent=self.groupBox)
        self.lineEdit_15.setGeometry(QtCore.QRect(220, 50, 91, 31))
        self.lineEdit_15.setObjectName("lineEdit_15")
        self.lineEdit_16 = QtWidgets.QLineEdit(parent=self.groupBox)
        self.lineEdit_16.setGeometry(QtCore.QRect(220, 120, 91, 31))
        self.lineEdit_16.setObjectName("lineEdit_16")
        self.label_14 = QtWidgets.QLabel(parent=self.groupBox)
        self.label_14.setGeometry(QtCore.QRect(30, 50, 181, 31))
        font = QtGui.QFont()
        font.setPointSize(15)
        self.label_14.setFont(font)
        self.label_14.setObjectName("label_14")
        self.label_16 = QtWidgets.QLabel(parent=self.groupBox)
        self.label_16.setGeometry(QtCore.QRect(30, 120, 181, 31))
        font = QtGui.QFont()
        font.setPointSize(15)
        self.label_16.setFont(font)
        self.label_16.setObjectName("label_16")
        self.groupBox_3 = QtWidgets.QGroupBox(parent=self.groupBox_2)
        self.groupBox_3.setGeometry(QtCore.QRect(30, 270, 361, 191))
        font = QtGui.QFont()
        font.setPointSize(18)
        self.groupBox_3.setFont(font)
        self.groupBox_3.setObjectName("groupBox_3")
        self.lineEdit_17 = QtWidgets.QLineEdit(parent=self.groupBox_3)
        self.lineEdit_17.setGeometry(QtCore.QRect(220, 50, 91, 31))
        self.lineEdit_17.setObjectName("lineEdit_17")
        self.lineEdit_18 = QtWidgets.QLineEdit(parent=self.groupBox_3)
        self.lineEdit_18.setGeometry(QtCore.QRect(220, 120, 91, 31))
        self.lineEdit_18.setObjectName("lineEdit_18")
        self.label_15 = QtWidgets.QLabel(parent=self.groupBox_3)
        self.label_15.setGeometry(QtCore.QRect(30, 50, 181, 31))
        font = QtGui.QFont()
        font.setPointSize(15)
        self.label_15.setFont(font)
        self.label_15.setObjectName("label_15")
        self.label_17 = QtWidgets.QLabel(parent=self.groupBox_3)
        self.label_17.setGeometry(QtCore.QRect(30, 120, 181, 31))
        font = QtGui.QFont()
        font.setPointSize(15)
        self.label_17.setFont(font)
        self.label_17.setObjectName("label_17")
        self.groupBox_4 = QtWidgets.QGroupBox(parent=Form)
        self.groupBox_4.setGeometry(QtCore.QRect(520, 30, 361, 241))
        font = QtGui.QFont()
        font.setPointSize(18)
        self.groupBox_4.setFont(font)
        self.groupBox_4.setObjectName("groupBox_4")
        self.lineEdit_19 = QtWidgets.QLineEdit(parent=self.groupBox_4)
        self.lineEdit_19.setGeometry(QtCore.QRect(220, 70, 91, 31))
        self.lineEdit_19.setObjectName("lineEdit_19")
        self.lineEdit_20 = QtWidgets.QLineEdit(parent=self.groupBox_4)
        self.lineEdit_20.setGeometry(QtCore.QRect(220, 170, 91, 31))
        self.lineEdit_20.setObjectName("lineEdit_20")
        self.label_18 = QtWidgets.QLabel(parent=self.groupBox_4)
        self.label_18.setGeometry(QtCore.QRect(30, 70, 181, 31))
        font = QtGui.QFont()
        font.setPointSize(15)
        self.label_18.setFont(font)
        self.label_18.setObjectName("label_18")
        self.label_19 = QtWidgets.QLabel(parent=self.groupBox_4)
        self.label_19.setGeometry(QtCore.QRect(30, 170, 181, 31))
        font = QtGui.QFont()
        font.setPointSize(15)
        self.label_19.setFont(font)
        self.label_19.setObjectName("label_19")
        self.pushButton = QtWidgets.QPushButton(parent=Form)
        self.pushButton.setGeometry(QtCore.QRect(600, 360, 201, 91))
        font = QtGui.QFont()
        font.setPointSize(20)
        self.pushButton.setFont(font)
        self.pushButton.setObjectName("pushButton")
        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)
    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "单次计算"))
        self.groupBox_2.setTitle(_translate("Form", "参数输入"))
        self.groupBox.setTitle(_translate("Form", "第一组实验"))
        self.label_14.setText(_translate("Form", "水平最小主应力："))
        self.label_16.setText(_translate("Form", "水平最小主应力："))
        self.groupBox_3.setTitle(_translate("Form", "第二组实验"))
        self.label_15.setText(_translate("Form", "水平最小主应力："))
        self.label_17.setText(_translate("Form", "水平最小主应力："))
        self.groupBox_4.setTitle(_translate("Form", "结果输出"))
        self.label_18.setText(_translate("Form", "内摩擦角："))
        self.label_19.setText(_translate("Form", "内聚力："))
        self.pushButton.setText(_translate("Form", "计算"))
import sys
import numpy as np
from Single_calculation import *
from PyQt6.QtWidgets import QApplication, QMainWindow
class single_calculationWindow(QMainWindow, Ui_Form):
    def __init__(self, parent=None):
        super(single_calculationWindow, self).__init__(parent)
        self.setupUi(self)
        self.pushButton.clicked.connect(self.caculate)
    def caculate(self):
        self.σ3_a = float(self.lineEdit_15.text())
        self.σ1_a = float(self.lineEdit_16.text())
        self.σ3_b = float(self.lineEdit_17.text())
        self.σ1_b = float(self.lineEdit_18.text())
        self.φ = np.arcsin(
            (self.σ1_b + self.σ3_a - self.σ3_b - self.σ1_a) / (self.σ1_b + self.σ3_b - self.σ1_a - self.σ3_a))
        self.C = (((self.σ1_b - self.σ3_b) * 0.5) / np.sin(self.φ) - (self.σ1_b + self.σ3_b) * 0.5) * np.tan(self.φ)
        self.lineEdit_19.setText(str(round(np.degrees(self.φ), 2)))
        self.lineEdit_20.setText(str(round(self.C, 2)))
if __name__ == "__main__":
    app = QApplication(sys.argv)
    my_win = single_calculationWindow()
    my_win.show()
sys.exit(app.exec())
from PyQt6 import QtCore, QtGui, QtWidgets
class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(709, 300)
        self.groupBox_4 = QtWidgets.QGroupBox(parent=Form)
        self.groupBox_4.setGeometry(QtCore.QRect(10, 20, 381, 241))
        font = QtGui.QFont()
        font.setPointSize(18)
        self.groupBox_4.setFont(font)
        self.groupBox_4.setObjectName("groupBox_4")
        self.lineEdit_19 = QtWidgets.QLineEdit(parent=self.groupBox_4)
        self.lineEdit_19.setGeometry(QtCore.QRect(170, 70, 201, 31))
        font = QtGui.QFont()
        font.setPointSize(15)
        self.lineEdit_19.setFont(font)
        self.lineEdit_19.setObjectName("lineEdit_19")
        self.lineEdit_20 = QtWidgets.QLineEdit(parent=self.groupBox_4)
        self.lineEdit_20.setGeometry(QtCore.QRect(170, 140, 91, 31))
        self.lineEdit_20.setObjectName("lineEdit_20")
        self.label_18 = QtWidgets.QLabel(parent=self.groupBox_4)
        self.label_18.setGeometry(QtCore.QRect(30, 70, 181, 31))
        font = QtGui.QFont()
        font.setPointSize(15)
        self.label_18.setFont(font)
        self.label_18.setObjectName("label_18")
        self.label_19 = QtWidgets.QLabel(parent=self.groupBox_4)
        self.label_19.setGeometry(QtCore.QRect(30, 140, 181, 31))
        font = QtGui.QFont()
        font.setPointSize(15)
        self.label_19.setFont(font)
        self.label_19.setObjectName("label_19")
        self.layoutWidget = QtWidgets.QWidget(parent=Form)
        self.layoutWidget.setGeometry(QtCore.QRect(450, 40, 231, 221))
        self.layoutWidget.setObjectName("layoutWidget")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.layoutWidget)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.pushButton = QtWidgets.QPushButton(parent=self.layoutWidget)
        font = QtGui.QFont()
        font.setPointSize(20)
        self.pushButton.setFont(font)
        self.pushButton.setObjectName("pushButton")
        self.verticalLayout.addWidget(self.pushButton)
        self.pushButton_2 = QtWidgets.QPushButton(parent=self.layoutWidget)
        font = QtGui.QFont()
        font.setPointSize(20)
        self.pushButton_2.setFont(font)
        self.pushButton_2.setObjectName("pushButton_2")
        self.verticalLayout.addWidget(self.pushButton_2)
        self.pushButton_3 = QtWidgets.QPushButton(parent=self.layoutWidget)
        font = QtGui.QFont()
        font.setPointSize(20)
        self.pushButton_3.setFont(font)
        self.pushButton_3.setObjectName("pushButton_3")
        self.verticalLayout.addWidget(self.pushButton_3)
        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)
    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "多组实验拟合"))
        self.groupBox_4.setTitle(_translate("Form", "结果输出"))
        self.label_18.setText(_translate("Form", "拟合直线方程："))
        self.label_19.setText(_translate("Form", "拟合优度："))
        self.pushButton.setText(_translate("Form", "实验数据导入"))
        self.pushButton_2.setText(_translate("Form", "结果计算"))
        self.pushButton_3.setText(_translate("Form", "图像绘制"))
import sys
import numpy as np
import pandas as pd
import statistics
import matplotlib.pyplot as plt
from scipy import stats
from Multiple_computation import *
from image_window import ImageWindow
from PyQt6.QtWidgets import QApplication, QMainWindow, QFileDialog
class Multiple_computationWindow(QMainWindow, Ui_Form):
    def __init__(self, parent=None):
        super(Multiple_computationWindow, self).__init__(parent)
        self.setupUi(self)
        self.image_windows = []
        self.pushButton.clicked.connect(self.file_input)
        self.pushButton_2.clicked.connect(self.caculate)
        self.pushButton_3.clicked.connect(self.fig_plot)
    def file_input(self):
        self.fileName, filetype = QFileDialog.getOpenFileName(self, "请选择实验数据：", '.', "实验数据 (*.xlsx)")
        while self.fileName == '':
            print('未选择数据，请重新选择：')
            self.fileName, filetype = QFileDialog.getOpenFileName(self, "请选择实验数据：", '.', "实验数据 (*.xlsx)")
    def caculate(self):
        data = pd.read_excel(self.fileName)
        print(data)
        list_σ = []
        list_C = []
        self.list_r_o = []
        for i in range(data.shape[0]):
            σ1_a = data.at[i, "强度"]
            σ3_a = data.at[i, "围压"]
            r_i = (σ1_a - σ3_a) / 2
            o_i = (σ1_a + σ3_a) / 2
            self.list_r_o.append([o_i, r_i])
            list_σ_temp = []
            list_C_temp = []
            for j in range(data.shape[0]):
                if i != j:
                    σ1_b = data.at[j, "强度"]
                    σ3_b = data.at[j, "围压"]
                    r_k = (σ1_b - σ3_b) / 2
                    o_k = (σ1_b + σ3_b) / 2
                    σ_i = o_i - r_i * ((r_k - r_i) / (o_k - o_i))
                    τ_i = np.sqrt(r_i ** 2 - (σ_i - o_i) ** 2)
                    list_σ_temp.append(round(σ_i, 2))
                    list_C_temp.append(round(τ_i, 2))
            list_σ.append(list_σ_temp)
            list_C.append(list_C_temp)
        medians = []
        corresponding_values = []
        for sublist1, sublist2 in zip(list_σ, list_C):
            median_value = statistics.median(sublist1)
            median_index = sublist1.index(median_value)
            corresponding_value = sublist2[median_index]
            medians.append(median_value)
            corresponding_values.append(corresponding_value)
        print(f'每个子列表的中位数是: {medians}')
        print(f'对应的另一个子列表的值是: {corresponding_values}')
        result = pd.DataFrame([medians, corresponding_values]).T
        result.columns = ['σ', 'τ']
        mean_x = result['σ'].mean()
        std_dev_x = result['σ'].std()
        mean_y = result['τ'].mean()
        std_dev_y = result['τ'].std()
        threshold_x = 3 * std_dev_x
        threshold_y = 3 * std_dev_y
        df_filtered = result[
            (np.abs(result['σ'] - mean_x) <= threshold_x) & (np.abs(result['τ'] - mean_y) <= threshold_y)]
        self.x_filtered = df_filtered['σ'].values
        self.y_filtered = df_filtered['τ'].values
        self.slope, self.intercept, r_value, p_value, std_err = stats.linregress(self.x_filtered, self.y_filtered)
        self.y_pred = self.intercept + self.slope * self.x_filtered
        r_squared = r_value ** 2
        self.lineEdit_19.setText(f'y = {self.slope:.2f}x + {self.intercept:.2f}')
        self.lineEdit_20.setText(f'{r_squared:.2f}')
        # print(f'拟合直线的方程: y = {slope:.2f}x + {intercept:.2f}')
        # print(f'拟合优度 (R^2): {r_squared:.2f}')
        outliers_x = result[~((np.abs(result['σ'] - mean_x) <= threshold_x))]
        outliers_y = result[~((np.abs(result['τ'] - mean_y) <= threshold_y))]
        print(f'被剔除的离群值 x:\n{outliers_x}')
        print(f'被剔除的离群值 y:\n{outliers_y}')
    def fig_plot(self):
        import matplotlib.patches as patches
        config = {
            "font.family": 'serif',
            "font.size": 12,
            "mathtext.fontset": 'stix',
            "font.serif": ['SimSun'],
        }
        plt.rcParams.update(config)
        fig, ax = plt.subplots()
        max_r = 0
        max_o = 0
        for o, r in self.list_r_o:
            circle = patches.Arc((o, 0), 2 * r, 2 * r, theta1=0, theta2=180, edgecolor='blue')
            ax.add_patch(circle)
            if o > max_o:
                max_o = o
                max_r = r
        x_fit = np.linspace(0, max_o + max_r + 20, 100)
        y_fit = self.intercept + self.slope * x_fit
        ax.scatter(self.x_filtered, self.y_filtered, color='blue', label='真实值')
        ax.plot(x_fit, y_fit, color='red', label='拟合直线')
        ax.set_xlim(-1, max_o + max_r + 20)
        ax.set_ylim(0, y_fit.max())
        ax.set_aspect('equal')
        plt.xlabel('σ', fontsize=20)
        plt.ylabel('τ', fontsize=20)
        plt.title('回归直线')
        plt.legend()
        plt.grid(True)
        equation = f'y = {self.slope:.2f}x + {self.intercept:.2f}'
        plt.text(0.45, 0.45, equation, transform=plt.gca().transAxes, fontsize=12, verticalalignment='top')
        window = ImageWindow(fig)
        self.image_windows.append(window)
if __name__ == "__main__":
    app = QApplication(sys.argv)
    my_win = Multiple_computationWindow()
    my_win.show()
sys.exit(app.exec())
from PyQt6.QtWidgets import QMainWindow, QVBoxLayout, QWidget
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg
class ImageWindow(QMainWindow):
    def __init__(self, fig, parent=None):
        super(ImageWindow, self).__init__(parent)
        self.initUI(fig)
    def initUI(self, fig):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        canvas = FigureCanvasQTAgg(fig)
        layout.addWidget(canvas)
        self.setCentralWidget(widget)
        self.setWindowTitle("Image Window")
        self.show()
