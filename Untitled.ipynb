{"cells": [{"cell_type": "code", "execution_count": 1, "id": "64b4decc", "metadata": {"ExecuteTime": {"end_time": "2024-06-29T08:56:06.645027Z", "start_time": "2024-06-29T08:56:06.294977Z"}}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 44, "id": "0b465ced", "metadata": {"ExecuteTime": {"end_time": "2024-06-29T09:31:47.200817Z", "start_time": "2024-06-29T09:31:47.174849Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>实验组别</th>\n", "      <th>围压</th>\n", "      <th>强度</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>实验1</td>\n", "      <td>0</td>\n", "      <td>80.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>实验2</td>\n", "      <td>5</td>\n", "      <td>143.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>实验3</td>\n", "      <td>10</td>\n", "      <td>163.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>实验4</td>\n", "      <td>20</td>\n", "      <td>192.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>实验5</td>\n", "      <td>40</td>\n", "      <td>254.4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>实验6</td>\n", "      <td>80</td>\n", "      <td>355.1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  实验组别  围压     强度\n", "0  实验1   0   80.5\n", "1  实验2   5  143.8\n", "2  实验3  10  163.2\n", "3  实验4  20  192.0\n", "4  实验5  40  254.4\n", "5  实验6  80  355.1"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["data = pd.read_excel(r'实验数据.xlsx')\n", "data"]}, {"cell_type": "code", "execution_count": 45, "id": "3f98f416", "metadata": {"ExecuteTime": {"end_time": "2024-06-29T09:31:48.008582Z", "start_time": "2024-06-29T09:31:48.000893Z"}}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["data.at[1,'围压']"]}, {"cell_type": "code", "execution_count": null, "id": "7f30fe08", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 135, "id": "f1ea3203", "metadata": {"ExecuteTime": {"end_time": "2024-06-30T01:53:32.073508Z", "start_time": "2024-06-30T01:53:32.052886Z"}}, "outputs": [], "source": ["list_σ = []\n", "list_C=[]\n", "list_r_o = []\n", "for i in range(data.shape[0]):\n", "    σ1_a = data.at[i, \"强度\"]\n", "    σ3_a = data.at[i, \"围压\"]\n", "    r_i = (σ1_a-σ3_a)/2\n", "    o_i = (σ1_a+σ3_a)/2\n", "    list_r_o.append([o_i,r_i])\n", "    list_σ_temp = []\n", "    list_C_temp = []\n", "    for j in range(data.shape[0]):\n", "        if i != j:\n", "            σ1_b = data.at[j, \"强度\"]\n", "            σ3_b = data.at[j, \"围压\"]\n", "            r_k = (σ1_b-σ3_b)/2\n", "            o_k = (σ1_b+σ3_b)/2\n", "            σ_i = o_i-r_i*((r_k-r_i)/(o_k-o_i))\n", "            τ_i = np.sqrt(r_i**2-(σ_i-o_i)**2)\n", "            list_σ_temp.append(round(σ_i,2))\n", "            list_C_temp.append(round(τ_i,2))\n", "    list_σ.append(list_σ_temp)\n", "    list_C.append(list_C_temp)"]}, {"cell_type": "code", "execution_count": 97, "id": "c6e728bb", "metadata": {"ExecuteTime": {"end_time": "2024-06-30T01:38:13.661810Z", "start_time": "2024-06-30T01:38:13.652789Z"}}, "outputs": [{"data": {"text/plain": ["[[40.25, 40.25],\n", " [74.4, 69.4],\n", " [86.6, 76.6],\n", " [106.0, 86.0],\n", " [147.2, 107.2],\n", " [217.55, 137.55]]"]}, "execution_count": 97, "metadata": {}, "output_type": "execute_result"}], "source": ["list_r_o"]}, {"cell_type": "code", "execution_count": 98, "id": "169dcb11", "metadata": {"ExecuteTime": {"end_time": "2024-06-30T01:38:14.063988Z", "start_time": "2024-06-30T01:38:14.045134Z"}}, "outputs": [{"data": {"text/plain": ["[[5.89, 8.68, 12.24, 15.05, 18.16],\n", " [15.16, 33.44, 37.94, 38.37, 41.36],\n", " [26.53, 41.39, 49.48, 47.92, 50.95],\n", " [46.16, 60.82, 64.33, 61.75, 66.26],\n", " [80.09, 91.54, 93.07, 92.04, 100.95],\n", " [142.06, 152.07, 153.53, 153.98, 158.21]]"]}, "execution_count": 98, "metadata": {}, "output_type": "execute_result"}], "source": ["list_σ"]}, {"cell_type": "code", "execution_count": 99, "id": "909ef65c", "metadata": {"ExecuteTime": {"end_time": "2024-06-30T01:38:14.590024Z", "start_time": "2024-06-30T01:38:14.574905Z"}}, "outputs": [{"data": {"text/plain": ["[[20.97, 24.97, 28.91, 31.39, 33.65],\n", " [36.15, 56.03, 59.05, 59.31, 61.03],\n", " [47.53, 61.84, 67.01, 66.12, 67.8],\n", " [61.77, 73.18, 75.23, 73.74, 76.27],\n", " [83.6, 91.62, 92.53, 91.92, 96.71],\n", " [114.99, 120.96, 121.74, 121.98, 124.09]]"]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}], "source": ["list_C"]}, {"cell_type": "code", "execution_count": 100, "id": "bff77f8a", "metadata": {"ExecuteTime": {"end_time": "2024-06-30T01:38:15.167022Z", "start_time": "2024-06-30T01:38:15.158181Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["12.24\n", "37.94\n", "47.92\n", "61.75\n", "92.04\n", "153.53\n"]}], "source": ["for i in list_σ:\n", "    print(np.median(i))\n", "    index = i.index(np.median(i))\n", "    "]}, {"cell_type": "code", "execution_count": 101, "id": "a4693d44", "metadata": {"ExecuteTime": {"end_time": "2024-06-30T01:38:16.207454Z", "start_time": "2024-06-30T01:38:16.200945Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["12.24\n", "37.94\n", "47.92\n", "61.75\n", "92.04\n", "153.53\n"]}], "source": ["for i in list_σ:\n", "    print(np.median(i))"]}, {"cell_type": "code", "execution_count": 102, "id": "ac2fae6c", "metadata": {"ExecuteTime": {"end_time": "2024-06-30T01:38:18.391681Z", "start_time": "2024-06-30T01:38:18.388841Z"}}, "outputs": [], "source": ["import statistics"]}, {"cell_type": "code", "execution_count": 103, "id": "1a4f6304", "metadata": {"ExecuteTime": {"end_time": "2024-06-30T01:38:19.758793Z", "start_time": "2024-06-30T01:38:19.752792Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["每个子列表的中位数是: [12.24, 37.94, 47.92, 61.75, 92.04, 153.53]\n", "对应的另一个子列表的值是: [28.91, 59.05, 66.12, 73.74, 91.92, 121.74]\n"]}], "source": ["# 用于存储结果的列表\n", "medians = []\n", "corresponding_values = []\n", "\n", "# 遍历每个子列表\n", "for sublist1, sublist2 in zip(list_σ, list_C):\n", "    # 计算子列表的中位数\n", "    median_value = statistics.median(sublist1)\n", "    \n", "    # 找到中位数的索引（如果中位数在列表中出现多次，这里取第一次出现的位置）\n", "    median_index = sublist1.index(median_value)\n", "    \n", "    # 获取另一个嵌套列表中对应位置的值\n", "    corresponding_value = sublist2[median_index]\n", "    \n", "    # 将结果添加到结果列表中\n", "    medians.append(median_value)\n", "    corresponding_values.append(corresponding_value)\n", "\n", "print(f'每个子列表的中位数是: {medians}')\n", "print(f'对应的另一个子列表的值是: {corresponding_values}')"]}, {"cell_type": "code", "execution_count": 127, "id": "d398be9d", "metadata": {"ExecuteTime": {"end_time": "2024-06-30T01:45:34.929949Z", "start_time": "2024-06-30T01:45:34.920947Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>σ</th>\n", "      <th>τ</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>12.24</td>\n", "      <td>28.91</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>37.94</td>\n", "      <td>59.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>47.92</td>\n", "      <td>66.12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>61.75</td>\n", "      <td>73.74</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>92.04</td>\n", "      <td>91.92</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>153.53</td>\n", "      <td>121.74</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        σ       τ\n", "0   12.24   28.91\n", "1   37.94   59.05\n", "2   47.92   66.12\n", "3   61.75   73.74\n", "4   92.04   91.92\n", "5  153.53  121.74"]}, "execution_count": 127, "metadata": {}, "output_type": "execute_result"}], "source": ["result = pd.DataFrame([medians,corresponding_values]).T\n", "result.columns = ['σ','τ']\n", "result"]}, {"cell_type": "code", "execution_count": 116, "id": "69f69be0", "metadata": {"ExecuteTime": {"end_time": "2024-06-30T01:43:32.295614Z", "start_time": "2024-06-30T01:43:32.288612Z"}}, "outputs": [], "source": ["from scipy import stats"]}, {"cell_type": "code", "execution_count": 117, "id": "8f107afc", "metadata": {"ExecuteTime": {"end_time": "2024-06-30T01:43:33.665726Z", "start_time": "2024-06-30T01:43:33.653222Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["拟合直线的方程: y = 0.62x + 31.79\n", "拟合优度 (R^2): 0.96\n"]}], "source": ["# 提取 x 和 y 列数据\n", "x = result['σ'].values\n", "y = result['τ'].values\n", "\n", "# 使用最小二乘法进行直线拟合\n", "slope, intercept, r_value, p_value, std_err = stats.linregress(x, y)\n", "\n", "# 计算拟合直线的预测值\n", "y_pred = intercept + slope * x\n", "\n", "# 计算拟合优度 (R^2)\n", "r_squared = r_value**2\n", "\n", "print(f'拟合直线的方程: y = {slope:.2f}x + {intercept:.2f}')\n", "print(f'拟合优度 (R^2): {r_squared:.2f}')\n"]}, {"cell_type": "code", "execution_count": 118, "id": "8a4ab0ab", "metadata": {"ExecuteTime": {"end_time": "2024-06-30T01:43:49.871764Z", "start_time": "2024-06-30T01:43:49.855122Z"}}, "outputs": [], "source": ["y_pred = intercept + slope * x"]}, {"cell_type": "code", "execution_count": 119, "id": "81ab08f7", "metadata": {"ExecuteTime": {"end_time": "2024-06-30T01:43:50.839945Z", "start_time": "2024-06-30T01:43:50.822344Z"}}, "outputs": [], "source": ["import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 120, "id": "8b5dba5d", "metadata": {"ExecuteTime": {"end_time": "2024-06-30T01:44:00.386053Z", "start_time": "2024-06-30T01:44:00.381413Z"}}, "outputs": [], "source": ["import matplotlib.patches as patches"]}, {"cell_type": "code", "execution_count": 128, "id": "a517590c", "metadata": {"ExecuteTime": {"end_time": "2024-06-30T01:45:38.068007Z", "start_time": "2024-06-30T01:45:37.919177Z"}}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAkcAAAHHCAYAAAC1G/yyAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/OQEPoAAAACXBIWXMAAA9hAAAPYQGoP6dpAABp2klEQVR4nO3dd1gUVxcG8HdZEAQE1KCAoiJq7L1EEUtE0VhDjBpNRGPsRok1JrFrrDHE3qJEo0ksaNQoih0b9hpjNLFFxU4RpO3e74/77bpLUZCFLby/5+HROzM7c86ysIczd2YVQggBIiIiIgIAWBk7ACIiIiJTwuKIiIiISAeLIyIiIiIdLI6IiIiIdLA4IiIiItLB4oiIiIhIB4sjIiIiIh0sjoiIiIh0sDgiIiIi0sHiyIzcvHkTCoUCISEhxg6FcqBXr14oU6aMscPIUJkyZdCrVy+TPm6ZMmXQrl273A0oFygUCkycOFE7DgkJgUKhwM2bN40WU36VndfbnTt3YGdnhyNHjuRuUHnoTX/Onzx5AgcHB+zYscPwQZkYFkcmQvOL8tSpU8YOJddMnDgRCoVC+2VjY4MyZcpg6NChiI6ONnZ4Fk9TXGf09c4772T4mD///BMTJ07M8zfw3D5ufHw8pkyZgurVq8Pe3h7Ozs7w9fXF6tWrkZNPVNqxY4deAWQqNL9fNF/W1tYoUaIEevXqhbt37xo7PJM2efJkNGjQAD4+Ptplmzdvhr+/Pzw8PGBra4uSJUuic+fOuHTpkkGPfeDAAb3vm1KpRLFixdC5c2dcuXLFoMfKiqJFi+Kzzz7DuHHj8vzYec3a2AFQ1pUuXRovXryAjY2NsUPJkcWLF8PR0RHx8fHYu3cv5s+fjzNnzuDw4cPGDi1PLF++HGq12mjH/+ijj/Dee+/pLXN1dQUAXL16FVZWL/9m+vPPPzFp0iQ0a9YsV7tdeXncBw8eoEWLFrhy5Qq6deuGIUOGIDExEZs2bUJgYCB27NiBtWvXQqlUZnvfO3bswMKFC7NUIH3yySfo1q0bbG1t3yCLNzN58mR4eXkhMTERx48fR0hICA4fPoxLly7Bzs4uz+IwF48ePcJPP/2En376SW/5xYsXUbhwYQwbNgxvvfUWoqKisHLlStSvXx/Hjh1DjRo1DBrH0KFDUa9ePaSkpODChQtYsmQJDhw4gEuXLsHNzc2gx3qdAQMGYN68edi3bx/efffdPD12XmJxZEYUCoXJ/wJLSEiAvb39K7fp3Lkz3nrrLQBA//790a1bN/z22284ceIE6tevnxdhAgDUajWSk5Pz/Dk1dnFbu3ZtfPzxxxmuy8s3amMdNzAwEFeuXMHmzZvRoUMH7fKhQ4di1KhRmDNnDmrVqoUxY8bkahxKpfKNCrDMxMfHw8HB4ZXbtGnTBnXr1gUAfPbZZ3jrrbcwc+ZMbN26FV26dDFYLJbi559/hrW1Ndq3b6+3fPz48em2/eyzz1CyZEksXrwYS5YsMWgcvr6+6Ny5s3b89ttvY+DAgVi9ejVGjx5t0GO9TqVKlVC1alWEhIRYdHHE02pmJKM5R7169YKjoyPu3r2LTp06wdHREa6urhg5ciRUKpXe49VqNYKDg1GlShXY2dmhePHi6N+/P549e6a33e+//462bdtqW8be3t6YMmVKuv01a9YMVatWxenTp9GkSRPY29vjq6++ynZevr6+AIB//vlHb3lkZCRat24NZ2dn2Nvbo2nTphme9z9w4ADq1q0LOzs7eHt7Y+nSpdpTeLoUCgWGDBmCtWvXokqVKrC1tUVYWBgA4O7du/j0009RvHhx2NraokqVKli5cmW6Y82fPx9VqlSBvb09ChcujLp162LdunXa9XFxcQgKCkKZMmVga2uLYsWKoWXLljhz5ox2m4zmHMXHx2PEiBHw9PSEra0t3n77bcyZMyfdKR5NDlu2bEHVqlW1sWryyCnduQghISH48MMPAQDNmzfXtvYPHDiQ4WO3bt0KhUKBCxcuaJdt2rQJCoUCAQEBettWqlQJXbt2zdFxDx8+jPr168POzg5ly5bF6tWrX5vf8ePHsWvXLvTq1UuvMNKYPn06ypcvj5kzZ+LFixcAXp7aSHv8tD+PvXr1wsKFCwFA71RIZjKbc7Rz5074+vrCwcEBhQoVQtu2bXH58mW9bTQ/9//88w/ee+89FCpUCD169Hht/mll9rP3119/oXPnzihSpAjs7OxQt25dbN26NcP4Dx8+jKFDh8LV1RUuLi7o378/kpOTER0djZ49e6Jw4cIoXLgwRo8ene71nJXXfdWqVdG8efN0savVapQoUUKvaJgzZw4aNWqEokWLomDBgqhTpw42btyY7edFY8uWLWjQoAEcHR1fu22xYsVgb2+fborAnTt30L17dxQrVgxWVlbpTmm/yRzSjL5vmc1lzOh3YUaio6MRFBSk/V6UK1cOM2fOzLDL3bJlS2zbti1Hp6BNHTtHFkClUsHf3x8NGjTAnDlzsGfPHnz33Xfw9vbGwIEDtdv1798fISEh6N27N4YOHYobN25gwYIFOHv2LI4cOaLtaISEhMDR0RHDhw+Ho6Mj9u3bh/HjxyM2NhazZ8/WO/aTJ0/Qpk0bdOvWDR9//DGKFy+e7fg1bw6FCxfWLtu3bx/atGmDOnXqYMKECbCyssKqVavw7rvvIiIiQtthOnv2LFq3bg13d3dMmjQJKpUKkydP1p4mSmvfvn1Yv349hgwZgrfeegtlypTBgwcP8M4772gLD1dXV+zcuRN9+vRBbGwsgoKCAMjTYUOHDkXnzp0xbNgwJCYm4sKFC4iMjET37t0ByJbzxo0bMWTIEFSuXBlPnjzB4cOHceXKFdSuXTvDmIQQ6NChA/bv348+ffqgZs2a2LVrF0aNGoW7d+/i+++/19v+8OHDCA0NxaBBg1CoUCHMmzcPH3zwAW7fvo2iRYu+9vlOSEjA48eP9ZY5Ozun62g1adIEQ4cOxbx58/DVV1+hUqVKAKD9N63GjRtDoVDg0KFDqF69OgAgIiICVlZWeqdMHz16hL/++gtDhgzJcD9ZOe7169fRuXNn9OnTB4GBgVi5ciV69eqFOnXqoEqVKpnmvm3bNgBAz549M1xvbW2N7t27Y9KkSThy5Aj8/Pwy3Vda/fv3x7179xAeHo41a9Zk+XG61qxZg8DAQPj7+2PmzJlISEjA4sWL0bhxY5w9e1bvzS81NRX+/v5o3Lgx5syZ89qObUYy+tm7fPkyfHx8UKJECXz55ZdwcHDA+vXr0alTJ2zatAnvv/++3j4+//xzuLm5YdKkSTh+/DiWLVsGFxcXHD16FKVKlcK3336LHTt2YPbs2ahatar2uc/q675r166YOHEioqKi9E4hHT58GPfu3UO3bt20y3744Qd06NABPXr0QHJyMn799Vd8+OGH2L59O9q2bZut5yYlJQUnT57U+x2aVnR0NFJSUhAVFYXg4GDExsaiRYsW2vWJiYnw8/PDtWvX0K9fP1SvXh179+5FaGgoihYtisaNG6NUqVLZigvI+PuWEwkJCWjatCnu3r2L/v37o1SpUjh69CjGjh2L+/fvIzg4WG/7OnXq4Pvvv8fly5dRtWpVg8RgcgSZhFWrVgkA4uTJk5luc+PGDQFArFq1SrssMDBQABCTJ0/W27ZWrVqiTp062nFERIQAINauXau3XVhYWLrlCQkJ6Y7dv39/YW9vLxITE7XLmjZtKgCIJUuWZCnHCRMmCADi6tWr4tGjR+LmzZti5cqVomDBgsLV1VXEx8cLIYRQq9WifPnywt/fX6jVar24vLy8RMuWLbXL2rdvL+zt7cXdu3e1y65duyasra1F2pc3AGFlZSUuX76st7xPnz7C3d1dPH78WG95t27dhLOzs/b56Nixo6hSpcorc3R2dhaDBw9+5TaBgYGidOnS2vGWLVsEADF16lS97Tp37iwUCoW4fv26Xg4FChTQW3b+/HkBQMyfP/+Vx9W8fjL62r9/vxBCiNKlS4vAwEDtYzZs2KC3/nWqVKkiunTpoh3Xrl1bfPjhhwKAuHLlihBCiNDQUAFAnD9/Xrtddo5bunRpAUAcOnRIu+zhw4fC1tZWjBgx4pXxderUSQAQz549y3QbTXzz5s0TQgixf//+DGPJ6Odx8ODB6V53GgDEhAkTtGPNz/yNGzeEEELExcUJFxcX0bdvX73HRUVFCWdnZ73lmp/7L7/88pX5pj3Wnj17xKNHj8SdO3fExo0bhaurq7C1tRV37tzRbtuiRQtRrVo1vZ91tVotGjVqJMqXL59un2l/Ths2bCgUCoUYMGCAdllqaqooWbKkaNq0qXZZVl/3V69ezfD1PWjQIOHo6Kj3+yrt767k5GRRtWpV8e677+otT/t6y8j169df+3P19ttva3+GHB0dxTfffCNUKpV2fUhIiAAgZs6cqfe4jz76SNjZ2YmHDx++MgbNa2/lypXi0aNH4t69eyIsLEyUK1dOKBQKceLECe22aX+vaGh+7+pKm/+UKVOEg4OD+Pvvv/W2+/LLL4VSqRS3b9/WW3706FEBQPz222+vjN+c8bSahRgwYIDe2NfXF//++692vGHDBjg7O6Nly5Z4/Pix9qtOnTpwdHTE/v37tdsWLFhQ+/+4uDg8fvwYvr6+SEhIwF9//aV3HFtbW/Tu3Ttbsb799ttwdXVFmTJl8Omnn6JcuXLYuXOn9i/fc+fO4dq1a+jevTuePHmijTU+Ph4tWrTAoUOHoFaroVKpsGfPHnTq1AkeHh7a/ZcrVw5t2rTJ8NhNmzZF5cqVtWMhBDZt2oT27dtDCKH33Pj7+yMmJkZ7SszFxQX//fcfTp48mWluLi4uiIyMxL1797L8fOzYsQNKpRJDhw7VWz5ixAgIIbBz50695X5+fvD29taOq1evDicnJ73v96v069cP4eHhel+GmkDq6+uLiIgIAPK1c/78efTr1w9vvfWWdnlERARcXFxy9Bdn5cqVtacWADmh/O23337tcxAXFwcAKFSoUKbbaNbFxsa+cXxvIjw8HNHR0fjoo4/0XodKpRINGjTQ+xnVeFVXIyN+fn5wdXWFp6cnOnfuDAcHB2zduhUlS5YEADx9+hT79u1Dly5dtD/7jx8/xpMnT+Dv749r166lu7qtT58+eqdtGjRoACEE+vTpo12mVCpRt25dve9PVl/3FSpUQM2aNfHbb79pt1GpVNi4cSPat2+v9/tK9//Pnj1DTEwMfH199U5rZ9WTJ08AvLo7s2rVKoSFhWHRokWoVKkSXrx4oTf9YP/+/bC2tsbgwYP1HqfpPKc9VZmZTz/9FK6urvDw8EDr1q0RExODNWvWoF69etnOKyMbNmyAr68vChcurPfa8/Pzg0qlwqFDh/S21zwnaTvQloSn1SyAnZ1dutNIhQsX1ptLdO3aNcTExKBYsWIZ7uPhw4fa/1++fBnffPMN9u3bl+4NIiYmRm9cokQJFChQIFvxbtq0CU5OTnj06BHmzZuHGzdu6P1Su3btGgA5cTYzMTExSExMxIsXL1CuXLl06zNaBgBeXl5640ePHiE6OhrLli3DsmXLMnyM5rkZM2YM9uzZg/r166NcuXJo1aoVunfvrneJ76xZsxAYGAhPT0/UqVMH7733Hnr27ImyZctmmsutW7fg4eGR7g1bcxrp1q1besszasOn/X6/Svny5bN1uig7fH19sWTJEly/fh3//PMPFAoFGjZsqC2a+vbti4iICPj4+OhdnZZdb/ocaJ7juLg4uLi4ZLhNVgqo3KB53Wc2ydXJyUlvbG1trS1qsmrhwoWoUKECYmJisHLlShw6dEhvMvz169chhMC4ceMyvVz74cOHKFGihHac9nvh7OwMAPD09Ey3XPf7k53XfdeuXfHVV1/h7t27KFGiBA4cOICHDx/qzVsDgO3bt2Pq1Kk4d+4ckpKStMuzMucmM+IV82oaNmyo/X+3bt20sc+ZMwcAcO/ePZQoUSLdRHnNdln9g2b8+PHw9fXF8+fPsXnzZvz66685+vlJ69q1a7hw4UKm0xF03x+Al89JTp5XU8fiyAJk5YoXtVqNYsWKYe3atRmu1/xQREdHo2nTpnBycsLkyZPh7e0NOzs7nDlzBmPGjEk3OU+3qMmqJk2aaK9Wa9++PapVq4YePXrg9OnTsLKy0h5j9uzZqFmzZob7cHR0RGJiYraPnTZezbE+/vjjTIsxzfyZSpUq4erVq9i+fTvCwsKwadMmLFq0COPHj8ekSZMAAF26dIGvry82b96M3bt3Y/bs2Zg5cyZCQ0Mz7WZlV2bf71f9Es8rjRs3BgAcOnQI//77L2rXrg0HBwf4+vpi3rx5eP78Oc6ePYtp06bl6Dhv+hxUqlQJW7ZswYULF9CkSZMMt9FMKNd0GDN7A0h7gUJOaV6La9asyfDybGtr/V/Xtra22X6DrF+/vvZqtU6dOqFx48bo3r07rl69CkdHR20MI0eOhL+/f4b7SPuHR2bfi4yWv+lrtGvXrhg7diw2bNiAoKAgrF+/Hs7OzmjdurV2m4iICHTo0AFNmjTBokWL4O7uDhsbG6xatUrvooms0szfy+ofHYULF8a7776LtWvXaosjpVKZ4etEsywlJSVL+65WrZr2D5pOnTohISEBffv2RePGjbVFaE5ep2q1Gi1btsz0yrcKFSrojTXPieb3uCVicZRPeHt7Y8+ePfDx8XllQXPgwAE8efIEoaGhem8eN27cyJW4HB0dMWHCBPTu3Rvr169Ht27dtKeMnJycXtnhKFasGOzs7HD9+vV06zJalhFXV1cUKlQIKpUqS90UBwcHdO3aFV27dkVycjICAgIwbdo0jB07VntLAHd3dwwaNAiDBg3Cw4cPUbt2bUybNi3T4qh06dLYs2cP4uLi9P6K1pzCLF26dJZyyQ3Z/cuwVKlSKFWqFCIiIvDvv/9qT301adIEw4cPx4YNG6BSqTItTN70uFnVrl07TJ8+HatXr84wBpVKhXXr1qFw4cLajqDmFELaq5DSdvSAnMWted0XK1Ys1zp7upRKJaZPn47mzZtjwYIF+PLLL7UdThsbm1yPITuvey8vL9SvXx+//fYbhgwZgtDQUHTq1Emv67Vp0ybY2dlh165destXrVr1RvGVKlUKBQsWzNbvvhcvXuh11729vREeHo6YmBhtR003x7Sd7KyaMWMGNm/ejGnTpmlvG1C4cOEMb6ab0es0LW9vbzx//jzL33PNc5LZxRmWgHOO8okuXbpApVJhypQp6dalpqZqf6g0f+3p/oWXnJyMRYsW5VpsPXr0QMmSJTFz5kwA8koIb29vzJkzB8+fP0+3/aNHj7Sx+vn5YcuWLXpzfK5fv55unk5mlEolPvjgA2zatCnDu9tqjgW8nIOgUaBAAVSuXBlCCKSkpEClUqU77VisWDF4eHjotfjTeu+996BSqbBgwQK95d9//z0UCoXBOk5vQnM6IDt3MPf19cW+fftw4sQJbXFUs2ZNFCpUCDNmzNBeYm3o42ZFo0aN4Ofnh1WrVmH79u3p1n/99df4+++/MXr0aO0fEaVLl4ZSqUw37yKjn4mcxO3v7w8nJyd8++23GXYUdF+LhtKsWTPUr18fwcHBSExMRLFixdCsWTMsXboU9+/fz9UYsvu679q1K44fP46VK1fi8ePH6U6pKZVKKBQKvU7JzZs3sWXLljeKz8bGBnXr1s3wUwvSnmbSHGvv3r3azhwgO+MqlQrz58/X23bu3LmwsrJ6459tb29vfPDBBwgJCUFUVJR2WUxMjN6tNO7fv4/Nmze/dn9dunTBsWPHsGvXrnTroqOjkZqaqrfs9OnTcHZ2fuWVoeaOnSMTs3LlygzvWTNs2LAc7bdp06bo378/pk+fjnPnzqFVq1awsbHBtWvXsGHDBvzwww/o3LkzGjVqhMKFCyMwMBBDhw6FQqHAmjVrcvWUjY2NDYYNG4ZRo0YhLCwMrVu3xooVK9CmTRtUqVIFvXv3RokSJXD37l3s378fTk5O2kuyJ06ciN27d8PHxwcDBw7U/rKtWrUqzp07l6Xjz5gxA/v370eDBg3Qt29fVK5cGU+fPsWZM2ewZ88ePH36FADQqlUruLm5wcfHB8WLF8eVK1ewYMECtG3bFoUKFUJ0dLT2YwRq1KgBR0dH7NmzBydPnsR3332X6fHbt2+P5s2b4+uvv8bNmzdRo0YN7N69G7///juCgoL0Jl/ntZo1a0KpVGLmzJmIiYmBra0t3n333UznrgGyOFq7di0UCoX2NJtSqUSjRo2wa9cuNGvW7LXz1N7kuFm1evVqtGjRAh07dkT37t3h6+uLpKQkhIaG4sCBA+jatStGjRql3d7Z2Rkffvgh5s+fD4VCAW9vb2zfvj3DN0hN0Td06FD4+/tDqVTqXWr+Kk5OTli8eDE++eQT1K5dG926dYOrqytu376NP/74Az4+PukKCUMYNWoUPvzwQ4SEhGDAgAFYuHAhGjdujGrVqqFv374oW7YsHjx4gGPHjuG///7D+fPnDXLc7L7uu3TpgpEjR2LkyJEoUqRIui5H27ZtMXfuXLRu3Rrdu3fHw4cPsXDhQpQrV06vYMiOjh074uuvv0ZsbKzenK9q1aqhRYsWqFmzJgoXLoxr167hxx9/REpKCmbMmKHdzt/fH++99x7Gjx+P27dvo2bNmggLC8O2bdswYsSIHN39fdSoUVi/fj2Cg4MxY8YMdOvWDWPGjMH777+PoUOHam8DUaFChddOSB81ahS2bt2Kdu3aaW+JER8fj4sXL2Ljxo24efOm3im08PBwtG/f3qLnHPFSfhOhuSw2s687d+5keim/g4NDuv1ldPmmEEIsW7ZM1KlTRxQsWFAUKlRIVKtWTYwePVrcu3dPu82RI0fEO++8IwoWLCg8PDzE6NGjxa5du9Jdzty0adPXXtqeUUyPHj1Kty4mJkY4OzvrXep79uxZERAQIIoWLSpsbW1F6dKlRZcuXcTevXv1Hrt3715Rq1YtUaBAAeHt7S1WrFghRowYIezs7PS2A5DpZfYPHjwQgwcPFp6ensLGxka4ubmJFi1aiGXLlmm3Wbp0qWjSpIk2Hm9vbzFq1CgRExMjhBAiKSlJjBo1StSoUUMUKlRIODg4iBo1aohFixbpHSujS27j4uLEF198ITw8PISNjY0oX768mD17tt4l0q/KISuXJmteP7Nnz850m4z2s3z5clG2bFmhVCqzdFn/5cuXBQBRqVIlveVTp04VAMS4ceNydNzSpUuLtm3bpttH06ZN9V4/rxIXFycmTpwoqlSpov1Z8PHxESEhIemecyGEePTokfjggw+Evb29KFy4sOjfv7+4dOlSup/H1NRU8fnnnwtXV1ehUCj0fgbxmkv5Nfbv3y/8/f2Fs7OzsLOzE97e3qJXr17i1KlT2m0y+7nPzKtuFaJSqYS3t7fw9vYWqampQggh/vnnH9GzZ0/h5uYmbGxsRIkSJUS7du3Exo0bX7vPzH7OM4o5q697DR8fHwFAfPbZZxmu//HHH0X58uWFra2tqFixoli1alWWLmXPzIMHD4S1tbVYs2ZNuhzr1q0rChcuLKytrYWHh4fo1q2buHDhQrp9xMfHi5EjR4oSJUpoc5wzZ06mOerSXMq/YcOGDNc3a9ZMODk5iejoaCGEELt37xZVq1YVBQoUEG+//bb4+eefs5x/XFycGDt2rChXrpwoUKCAeOutt0SjRo3EnDlzRHJysna7K1euaG8LYckUQpjALE4iA+vUqRMuX76svQKIiOhN9OnTB3///bf2VhT5XVBQEA4dOoTTp09bdOeIc47I7Gk+5kHj2rVr2LFjB5o1a2acgIjIYkyYMAEnT57M8KOL8psnT55gxYoVmDp1qkUXRgDAzhGZPXd3d/Tq1Qtly5bFrVu3sHjxYiQlJeHs2bMoX768scMjIiIzwwnZZPZat26NX375BVFRUbC1tUXDhg3x7bffsjAiIqI3ws4RERERkQ7OOSIiIiLSweKIiIiISAfnHEF+rsy9e/dQqFAhi5+BT0REZCmEEIiLi4OHh4dBP4yXxRHkJyen/QRpIiIiMg937txByZIlDbY/FkeA9kMP79y5o3eLeHOQkpKC3bt3az8OJL9h/vk3//ycO8D8mT/z3717Nxo2bAgvLy+9Dy82BBZHePlJ2k5OTmZZHNnb28PJySnf/oAw//yZf37OHWD+zJ/529vba4siQ0+J4YRsIiIiIh0sjoiIiIh0sDgiIiIi0sE5R1mkVquRnJxs7DDSSUlJgbW1NRITE6FSqYwdTp4zlfxtbGygVCqNdnwiIjIcFkdZkJycjBs3bkCtVhs7lHSEEHBzc8OdO3fy5T2aTCl/FxcXuLm5GT0OIiLKGRZHryGEwP3796FUKuHp6WnQm0wZglqtxvPnz+Ho6GhyseUFU8hfCIGEhAQ8fPgQAODu7m6UOIiIyDBYHL1GamoqEhIS4OHhAXt7e2OHk47mdJ+dnV2+LY5MIf+CBQsCAB4+fIhixYrxFBsRkRnLf++m2aSZx1KgQAEjR0KmTlM8p6SkGDkSIiLKCRZHWcR5JPQ6fI0QEVkGFkdEREREOlgc5UPNmjVDUFCQdlymTBkEBwcbLZ6MKBQKbNmyxdhhEBFRPsTiyEL16tULCoUi3df169cRGhqKKVOmZPpYcyhMNPkplUq4urrC3d0dLVu2xMqVK7N9y4WQkBC4uLjkTqBERGR2WBzlEZUKOHAA+OUX+W9e3K+wdevWuH//vt6Xl5cXihQpYvBPMM5Ibk9Mbt26Ne7evYvz58/jjz/+QPPmzTFs2DC0a9cOqampuXpsIiLKothY+cZnRlgc5YHQUKBMGaB5c6B7d/lvmTJyeW6ytbWFm5ub3pdSqUx3Wk1XmTJlAADvv/8+FAqFdgwAv//+O2rXrg07OzuULVsWkyZN0itCFAoFFi9ejA4dOsDBwQHTpk3L0uOuXbuGJk2awM7ODpUrV0Z4eHi28vPw8EDt2rXx1Vdf4ffff8fOnTsREhKi3W7u3LmoVq0aHBwc4OnpiUGDBuH58+cAgAMHDqB3796IiYnRdtcmTpwIAFizZg3q1q2LQoUKwc3NDd27d9fey4iIiLJgzx6gWjWgXTvgxg1jR5NlLI5yWWgo0Lkz8N9/+svv3pXLc7tAyq6TJ08CAFatWoX79+9rxxEREejZsyeGDRuGP//8E0uXLkVISIi2ANKYOHEi3n//fVy8eBGffvrpax+nVqsREBCAAgUKIDIyEkuWLMGYMWPeOP53330XNWrUQKjOE2tlZYV58+bh8uXL+Omnn7Bv3z6MHj0aANCoUSMEBwfDyclJ210bOXIkANn5mjJlCs6fP48tW7bg5s2b6NWr1xvHRkSUb8TGAv37Ay1bArdvA8WKAU+eGDuqLONNIHORSgUMGwYIkX6dEIBCAQQFAR07Arlxz8Dt27fD0dFRO27Tpg02bNjwyse4uroCePlRGBqTJk3Cl19+icDAQABA2bJlMWXKFIwePRoTJkzQbte9e3f07t1bO/70009f+bg9e/bgr7/+wq5du+Dh4QEA+Pbbb9GmTZs3zrtixYq4cOGCdpx28vnUqVMxYMAALFq0CAUKFICzszMUCoVevprYNcqWLYt58+ahXr162jtyExFRBsLDgc8+k0URAAwZAkyfDpjR700WR7koIiJ9x0iXEMCdO3K7Zs0Mf/zmzZtj8eLF2rGDg8Mb7+v8+fM4cuSIXqdIpVIhMTERCQkJ2hsg1q1bN1uPu3LlCjw9PbWFEQA0bNjwjeME5Md56N5zaM+ePZg+fTr++usvxMbGIjU1NV3cGTl9+jQmTpyI8+fP49mzZ9qJ3rdv30blypVzFCMRkcWJjQVGjgSWL5fjsmWBlSuBpk2NG9cbYHGUi+7fN+x22eXg4IBy5coZZF/Pnz/HpEmTEBAQkG6dnZ2d3jHf5HGGdOXKFXh5eQEAbt68iXbt2mHgwIGYNm0aihQpgsOHD6NPnz5ITk7OtDiKj4+Hv78//P39sXbtWri6uuL27dvw9/dHcnJyrsRNRGS2du+W3aI7d+T4889ltygHf5QbE4ujXJTVzx81tc8ptbGx0X5sikbt2rVx9erVbBdbr3tcpUqVcOfOHdy/f1/7ga3Hjx9/s8AB7Nu3DxcvXsQXX3wBQHZ/1Go1vvvuO+1nr61fv17vMQUKFEiX719//YUnT55gxowZ8PT0BACcOnXqjeMiIrJIMTGyW7RihRybcbdIF4ujXOTrC5QsKSdfZzTvSKGQ63198z62VylTpgz27t0LHx8f2NraonDhwhg/fjzatWuHUqVKoXPnzrCyssL58+dx6dIlTJ06NdN9ve5xfn5+qFChAgIDAzF79mzExsbi66+/zlKcSUlJiIqKQnR0NK5fv47du3dj+vTpaNeuHXr27AkAKFeuHFJSUjB//ny0b98eR44cwZIlS9Ll+/z5c+zduxc1atSAvb09SpUqhQIFCmD+/PkYMGAALl269Mp7QxER5Tu7dslukWb+yNChwLffmm23SBevVstFSiXwww/y/2k/dkszDg7OncnYOfHdd98hPDwcnp6eqFWrFgDA398f27dvx+7du1GvXj288847+P7771G6dOlX7ut1j7OyssLmzZvx4sUL1K9fH5999lm6K+AyExYWhhIlSqBGjRp47733sH//fsybNw+///47lP9/UmvUqIG5c+di5syZqFq1KtauXYvp06fr7adRo0YYMGAAunbtCldXV8yaNQuurq4ICQnBhg0bULlyZcyYMQNz5szJ7lNJRGR5YmJkUdS6tSyMvL2BgwflG54FFEYAAEEiJiZGABAxMTHp1r148UL8+eef4sWLF2+8/02bhChZUgjZP5Jfnp5yeU6pVCrx7NkzoVKpcr4zM2RK+RvitZJdycnJYsuWLSI5OTnPjmkq8nPuQjB/5m+k/Hfu1H9DGzpUiOfP8zYG8TL/x48fZ/r+nRM8rZYHAgLk5foREXLytbu7PJVmah0jIiKiDMXEAMOHy/lEgOwWrVwJNGli3LhyCYujPKJU5s7l+kRERLlq506gXz95Ck2hkDfwmzYNeMWtUMwdiyMiIiJKLzoaGDHiZbeoXDn5f1O7iigXcEI2ERER6du5E6haVRZDmo9zOH8+XxRGADtHREREpBEdLecWrVolx+XLywKpceM32p1KZZ7zbdk5IiIiImDHDtktWrVKdou++AI4d+6NC6PQUKBMGaB5c6B7d/lvmTKm94HrGWFxRERElJ9FRwO9ewNt28q7FpcvL9s9c+e+8aTr0FCgc+f0ny96965cbuoFEosjIiKi/OqPP4AqVYCQENktGj5cdot8fN54lyqVvKAto0+G0CwLCpLbmSoWR0RERPnNs2dAr15Au3bAvXtAhQrA4cPAd9/l+BL9iIj0HSNdQsjPp42IyNFhchWLo3yoWbNmCAoKyrPjhYSEwMXFJdP1N2/ehEKhwLlz5wAABw4cgEKhQHR0dJ7ER0SUr/zxh5xb9NNPsls0YoTsFjVqZJDd379v2O2MgcWRherVqxcUCkW6r+vXryM0NFTvQ1TLlCmD4OBgvce/rqDJTY0aNcL9+/fh7OxslOMTEVmkZ8+AwMD03aI5c4CCBQ12GHd3w25nDEYtjg4dOoT27dvDw8MDCoUCW7Zs0a5LSUnBmDFjUK1aNTg4OMDDwwM9e/bEvXv39Pbx9OlT9OjRA05OTnBxcUGfPn3w/PnzPM7ENLVu3Rr379/X+/Ly8kKRIkVQqFAhY4eXqQIFCsDNzQ2KtJ/WS0REb2b7djm3aPVq2S0aOdKg3SJdvr5AyZLpP3BdQ6EAPD1N+5ZJRi2O4uPjUaNGDSxcuDDduoSEBJw5cwbjxo3DmTNnEBoaiqtXr6JDhw562/Xo0QOXL19GeHg4tm/fjkOHDqFfv355lYJJs7W1hZubm96XUqnUO63WrFkz3Lp1C1988YW2u3TgwAH07t0bMTEx2mUTJ04EACQlJWHkyJEoUaIEHBwc0KBBAxw4cEDvuCEhIShVqhTs7e3x/vvv48mTJ9mKO+1pNU0Xa9euXahUqRIcHR21hZ+uFStWoFKlSrCzs0PFihWxaNGiN3naiIgsx7NnQM+eQPv28jzW228DR44As2cbtFukS6kEfvhB/j9tgaQZBweb9v2OjHoTyDZt2qBNmzYZrnN2dkZ4eLjesgULFqB+/fq4ffs2SpUqhStXriAsLAwnT55E3bp1AQDz58/He++9hzlz5sDDw8PwQQsBJCQYfr9ZYW+feSn+hkJDQ1GjRg3069cPffv2BQAUKVIEwcHBGD9+PK5evQoAcHR0BAAMGTIEf/75J3799Vd4eHhg8+bNaN26NS5evIjy5csjMjISffr0wfTp09GpUyeEhYVhwoQJOY4zISEBc+bMwZo1a2BlZYWPP/4YI0eOxJo1awAAa9euxfjx47FgwQLUqlULZ8+eRd++feHg4IDAwMAcH5+IyOxs2wb07y+LIisreSXa5Mm5VhTpCggANm6UV63pTs4uWVIWRgEBuR5CjpjVHbI1nQzNXJhjx47BxcVFWxgBgJ+fH6ysrBAZGYn3338/w/0kJSUhKSlJO46NjQUgT+WlpKTobZuSkgIhBNRqNdRqNRAfDysnJwNnljXq2FjAwUFvmfj/dZGaGHWXb9++XVvUAPI02/r16/W2d3FxgVKphKOjI4oVK6bdtlChQlAoFHrLbt68iVWrVuHmzZvawnP48OEICwvDypUrMW3aNAQHB8Pf3x8jR44EIIupI0eOYNeuXXrx6eX1/+Wa5zijcUpKChYtWgRvb28AwODBgzFlyhRt/pMmTcLs2bPRqVMnAEDp0qVx+fJlLF26FJ988kl2nuY3plarIYRASkoKlHn0J5Hm9Zr2dZsf5OfcAebP/F+R/9OnUI4YAau1awEAokIFqFasgHjnHc2D8yTG9u2B994Djh0DoqIANzegYUPZMcppCLn9/Teb4igxMRFjxozBRx99BKf/FydRUVF6b94AYG1tjSJFiiAqKirTfU2fPh2TJk1Kt3z37t2wT3MJo7W1Ndzc3PD8+XMkJycD8fFwyXk6byQ2NjbTG0PExcXpjVNSUuDr64vvvvtOu8ze3h6xsbFITU1FcnKytihUq9VITEzUjgH5fAsh9JZFRkZCpVKhYsWKesdKSkqCk5MTYmNjcfnyZbRr107vcbVq1UJYWJjeMl2aOWLx8fGIjY1Fwv87c3FxcbCyskJiYiLs7e3h6uqq3YezszMePnyIuLg4xMfH459//kHfvn3Rv39/7X5TU1O1ceWF5ORkvHjxAocOHUJqamqeHFMjbZc1P8nPuQPMn/nr5+924gRqLF4Mm2fPIKyscL1jR/zVrRvUT5/KO2Abib09EBsL7Npl2P3u37/fsDv8P7MojlJSUtClSxcIIbB48eIc72/s2LEYPny4dhwbGwtPT0+0atVKW3hpJCYm4s6dO3B0dISdnR1QqJDs4BiBUwan1YQQiIuL03Z6NGxsbODk5ISaNWum24+1tTUKFCigzdXKygp2dnZ6udvZ2UGhUOgtU6vVUCqVOHnyZLrOiKOjI5ycnKBUKmFra/vafaV9LAA4ODjAyclJW6AWKlQITk5OsLOz0+ajYW9vDyEEChUqhIcPHwIAli5digYNGujtW6lUZnpcQ0tMTETBggXRpEkT+VrJAykpKQgPD0fLli1hY2OTJ8c0Ffk5d4D5M/80+T99CuXw4bBatw4AIN5+G6oVK1CmQQOUMW6ouUKTf/PmzXNl/yZfHGkKo1u3bmHfvn16b3Rubm7aN0aN1NRUPH36FG5ubpnu09bWFra2tumW29jYpPshU6lUUCgUsLKygpXV/+evm9CVXppTUJoYNTQTqXWX6dJdV6BAAajVar1t7ezsoFKp9JbVqVMHKpUKjx8/hm8mlxlUqlQJJ06c0HtcZGQkAGQai2a55jl+1TjtYzSn/jw8PHDz5s08O4WWESsrKygUigxfR7nNGMc0Ffk5d4D5M38b2OzcKecWRUXJuUUjR0IxaRKs8+iPNGPKre+9SRdHmsLo2rVr2L9/P4oWLaq3vmHDhoiOjsbp06dRp04dAMC+ffugVqvTdRAoc2XKlMGhQ4fQrVs32Nra4q233kKZMmXw/Plz7N27FzVq1IC9vT0qVKiAHj16oGfPnvjuu+9Qq1YtPHr0CHv37kX16tXRtm1bDB06FD4+PpgzZw46duyIXbt2ISwsLNdzmDBhAoKCguDs7IzWrVsjKSkJp06dwrNnz/S6hERElsImNhbKwEDgl1/kgooV5ceA8P0vx4x6Kf/z589x7tw57Z2Rb9y4gXPnzuH27dtISUlB586dcerUKaxduxYqlQpRUVGIioqSc38guxStW7dG3759ceLECRw5cgRDhgxBt27dcudKNQs1efJk3Lx5E97e3nB1dQUgb8Q4YMAAdO3aFa6urpg1axYAYNWqVejZsydGjBiBt99+G506dcLJkydRqlQpAMA777yD5cuX44cffkCNGjWwe/dufPPNN7mew2effYYVK1Zg1apVqFatGpo2bYqQkBB4eXnl+rGJiPKaYutWvDt0KKx++UV2i8aMAc6eZWFkKMKI9u/fLwCk+woMDBQ3btzIcB0AsX//fu0+njx5Ij766CPh6OgonJycRO/evUVcXFy24oiJiREARExMTLp1L168EH/++ad48eJFTtPNFSqVSjx79kyoVCpjh2IUppS/MV4rycnJYsuWLSI5OTnPjmkq8nPuQjD/fJv/48dCdO8uhLyxjFBXrChEZKSxo8pzmu//48ePM33/zgmjnlZr1qyZ9lLsjLxqnUaRIkWw7v8T0IiIiCzWli3AgAHAgwcQVla49v778Fq1CjYmNA/WUpj0nCMiIqJ878kT4PPPX84tqlwZquXLceXRI3jlg0nXxsAPniUiIjJVmzcDlSvLwsjKChg7Fjh9GqJePWNHZtHYOSIiIjI1jx/LbtGvv8px5crySjRNUZRP7wyeV9g5yqKszH+i/I2vESIyiNBQoEoVWRgplcBXXwFnzrwsjCjXsXP0Gpo7QScnJ6NgHnxYH5kvzcee5Ocb0hFRDjx+DAwZAvz2mxxXqSK7RTqfH0p5g8XRa1hbW8Pe3h6PHj2CjY1Npnd5Nha1Wo3k5GQkJiaaXGx5wRTyF0IgISEBDx8+1H6QLxFRtmzaBAwcCDx6JLtFY8YA48cDGXyaA+U+FkevoVAo4O7ujhs3buDWrVvGDicdIQRevHiBggUL6n22Wn5hSvm7uLi88mNriIjSefRIzi3SdIuqVgVWrWK3yMhYHGVBgQIFUL58ee2duU1JSkoKDh06hCZNmuTL0zmmkr+NjQ07RkSUPRs3AoMGvewWffklMG4cu0UmgMVRFmk+ud7UKJVKpKamaj+5Pr/J7/kTkRl69EjOLVq/Xo6rVpVzi/7/GaFkfPlvkgoREZGxbNwoJ1qvXy+7Rd98A5w6xcLIxLBzRERElNsePQIGDwY2bJDjatVkt6h2baOGRRlj54iIiCg3bdggb+K4YYPsFo0bJ7tFLIxMFjtHREREueHhQ9kt2rhRjqtXl1eisSgyeewcERERGdr69XJu0caNgLW1vGfRyZMsjMwEO0dERESGklG3KCQEqFXLqGFR9rBzRERElFNCyBs5Vq6cvlvEwsjssHNERESUEw8eyJs5hobKcY0acm4RiyKzxc4RERHRmxAC+PVXObcoNFR2iyZOBE6cYGFk5tg5IiIiyq6MukUhIUDNmsaMigyEnSMiIqKselW3iIWRxWDniIiIKCuiomS3aPNmOa5ZU3aLatQwZlSUC9g5IiIiehUhgF9+kd2izZtlt2jSJNktYmFkkdg5IiIiykxUFDBwILBlixzXqiWvRGNRZNHYOSIiIkpLCGDdOtkt2rIFsLEBJk8GIiNZGOUD7BwRERHpiooCBgwAfv9djmvVknOLqlc3aliUd9g5IiIiAmS3aO1aeZfr33+X3aIpU2S3iIVRvsLOERER0f37slu0dasc164t5xaxKMqX2DkiIqL8Swjg55/l3KKtW2W3aOpU4PhxFkb5GDtHRESUP2XULQoJAapVM2pYZHzsHBERUf4iBLBmjZxblLZbxMKIwM4RERHlJ/fuyW7Rtm1yXKeO7BZVrWrUsMi0sHNERESWTwhg9Wo5t2jbNqBAAeDbb2W3iIURpcHOERERWbZ794D+/YHt2+W4bl15JRqLIsoEO0dERGSZdLtF27e/7BYdO8bCiF6JnSMiIrI8d+/KbtEff8hx3bpyblGVKkYNi8wDO0dERGQ5hAB++kkWQX/8IbtF06fLbhELI8oido6IiMgy3L0L9OsH7Nghx/XqyW5R5cpGDYvMDztHRERk3oR4ecpsxw7ZLZoxAzh6lIURvRF2joiIyHzdvQsMHvyyW1S/vrwSjUUR5QA7R0REZH6EQKm9e2Fds6YsjGxtgZkzgSNHWBhRjrFzRERE5uW//6Ds2xe1wsLkuH59eVqtUiWjhkWWg50jIiIyD0IAK1cCVarAKiwMKhsbqKZPl90iFkZkQOwcERGR6btzR16J9v9ukbp+fRz45BM06d8fSmu+lZFhsXNERESmSwjgxx/lHa3DwuTcolmzoDp4EM89PY0dHVkolttERGSa7twB+vYFdu2S43fekVeiVawIpKQYNzayaOwcERGRaRECWLFC3rdo1y7ZLZo9Gzh8WBZGRLmMnSMiIjIdt2/LbtHu3XLcsKGchM2iiPIQO0dERGR8mm5R1aqyMLKzA+bMASIiWBhRnmPniIiIjCujbtGqVcDbbxs3Lsq32DkiIiLjEAJYvly/W/Tdd7JbxMKIjIidIyIiynu3bsluUXi4HDdqJLtFFSoYNy4isHNERER5SQhg2TKgWjVZGNnZAXPnAocOsTAik8HOERER5Y1bt4DPPgP27JFjHx95JRqLIjIx7BwREVHuEgJYulTOLdqzByhYEPj+e+DgQRZGZJLYOSIiotxz6xbQpw+wd68c+/jIuUXlyxs3LqJXYOeIiIgMTwhgyRLZLdq7V3aLgoNlt4iFEZk4do6IiMiwbt6U3aJ9++S4cWM5t4hFEZkJo3aODh06hPbt28PDwwMKhQJbtmzRWy+EwPjx4+Hu7o6CBQvCz88P165d09vm6dOn6NGjB5ycnODi4oI+ffrg+fPneZgFEREBANRqYPFi2S3at4/dIjJbRi2O4uPjUaNGDSxcuDDD9bNmzcK8efOwZMkSREZGwsHBAf7+/khMTNRu06NHD1y+fBnh4eHYvn07Dh06hH79+uVVCkREBAA3bgAtWwKDBgHx8YCvL3DhAjBsGGDFGRxkXox6Wq1NmzZo06ZNhuuEEAgODsY333yDjh07AgBWr16N4sWLY8uWLejWrRuuXLmCsLAwnDx5EnXr1gUAzJ8/H++99x7mzJkDDw+PPMuFiChfUqvllWijRsmiqGBBYMYMYMgQFkVktkz2lXvjxg1ERUXBz89Pu8zZ2RkNGjTAsWPHAADHjh2Di4uLtjACAD8/P1hZWSEyMjLPYyYiyldu3AD8/F52i5o0kd2ioUNZGJFZM9kJ2VFRUQCA4sWL6y0vXry4dl1UVBSKFSumt97a2hpFihTRbpORpKQkJCUlacexsbEAgJSUFKSkpBgk/ryiidfc4jYU5p9/88/PuQNGzl+thtWyZbAaOxaK+HgIe3uop02DeuBAWRTlQUz8/jN/3X8NzWSLo9w0ffp0TJo0Kd3y3bt3w97e3ggR5Vy45vOJ8inmn3/zz8+5A3mfv/2DB6g5fz5cL10CADyuUgVnhwxBgrs7EBaWp7EA/P7n9/z379+fK/s12eLIzc0NAPDgwQO4u7trlz948AA1a9bUbvPw4UO9x6WmpuLp06fax2dk7NixGD58uHYcGxsLT09PtGrVCk5OTgbMIvelpKQgPDwcLVu2hI2NjbHDyXPMP//mn59zB4yQv1oNq6VLYfXVVy+7Rd9+C+cBA9DMCKfQ+P1n/uHh4WjevHmu7N9kiyMvLy+4ublh79692mIoNjYWkZGRGDhwIACgYcOGiI6OxunTp1GnTh0AwL59+6BWq9GgQYNM921rawtbW9t0y21sbMz2RWbOsRsC88+/+efn3IE8yv/ff4FPP5WX5ANA06ZQ/PgjlN7eUObukV+L33/mnxuMWhw9f/4c169f145v3LiBc+fOoUiRIihVqhSCgoIwdepUlC9fHl5eXhg3bhw8PDzQqVMnAEClSpXQunVr9O3bF0uWLEFKSgqGDBmCbt268Uo1IqKcUquBRYuAMWOAhATA3h6YNQvQzC0islBGLY5OnTql1xLTnOoKDAxESEgIRo8ejfj4ePTr1w/R0dFo3LgxwsLCYGdnp33M2rVrMWTIELRo0QJWVlb44IMPMG/evDzPhYjIovzzj7zLtaZb1KwZ8OOPQNmyRg2LKC8YtThq1qwZhBCZrlcoFJg8eTImT56c6TZFihTBunXrciM8IqL8R60GFi4EvvxSdoscHICZM9ktonzFZOccERFRHvvnHzm36NAhOWa3iPIp/hlARJTfqdXAvHlA9eqyMHJwkHON9u5lYUT5EjtHRET52fXrslsUESHHzZvLbpGXl3HjIjIido6IiPIj3W5RRMTLbtGePSyMKN9j54iIKL9J2y16913ZLSpTxqhhEZkKdo6IiPILtRr44YeX3SJHR2DxYtktYmFEpMXOERFRfnDtmuwWHT4sxy1aACtWsCgiygA7R0RElkylAoKDgRo1ZGHk6AgsWQKEh7MwIsoEO0dERJbq2jWgd2/gyBE5ZreIKEvYOSIisjQqFfD993Ju0ZEjslu0dCm7RURZxM4REZEl+ftvObdI0y3y85PdotKljRsXkRlh54iIyBKoVMDcuXJu0ZEjQKFCwLJlwO7dLIyIsomdIyIic3f1quwWHT0qxy1bym5RqVLGjYvITLFzRERkrlQqWH3/PVCzpiyMNN2iXbtYGBHlADtHRETm6OpV+H71FZRXr8pxq1bA8uUsiogMgJ0jIiJzolIBc+bAul49FLl6FaJQIVkUhYWxMCIyEHaOiIjMxV9/yfsWHT8OBYAHtWqhyIYNsPH2NnZkRBaFxRERkQGpVPJjy+7fB9zdAV9fQKk0wE6//x745hsgKQlwckLq7Nk4XqwY3mO3iMjgeFqNiMhAQkPlPRabNwe6d5f/likjl7+xv/4CGjcGRo2ShZG/P3DpEkTv3oBCYaDIiUgXiyMiIgMIDQU6dwb++09/+d27cnm2CySVCpg9W16Jdvw44OQE/PgjsHMn4OlpqLCJKAMsjoiIckilAoYNA4RIv06zLChIbpclV64APj7A6NGyW9S6NXD5sryXEbtFRLmOxRERUQ5FRKTvGOkSArhzR273SioVMGsWUKsWEBkpu0UrVwI7dgAlSxo0ZiLKHCdkExHl0P37BtjuyhV5JVpkpBy3aSNv6MiiiCjPsXNERJRD7u452C41FZg582W3yNkZWLUK+OMPFkZERsLOERFRDvn6yjrm7t2M5x0pFHK9r2+aFX/+KbtFJ07I8XvvyW5RiRK5HjMRZY6dIyKiHFIqgR9+kP9PO19aMw4O1rnfUWoqMGOG7BadOCG7RSEhwPbtLIyITACLIyIiAwgIADZuTF/blCwplwcE/H/B5ctAo0bA2LFAcrLsFl2+DAQG8ko0IhPB02pERAYSEAB07JjJHbJTU+V9iyZOlEWRs7NsN/XsyaKIyMSwOCIiMiClEmjWLM3Cy5eBXr2AU6fkuG1bYOlSnkIjMlE8rUZElFtSU4Hp04HatWVh5OIC/PQTsG0bCyMiE8bOERFRbrh0SV6JpukWtWsnu0UeHsaNi4hei50jIiJDSk0Fvv0WqFPnZbdo9Wpg61YWRkRmgp0jIiJDuXRJzi06fVqO27eX3aKs3iWSiEwCO0dERDmVkgJMmybnFp0+DRQuLLtFv//OwojIDLFzRESUExcvym7RmTNyzG4Rkdlj54iI6E2kpABTp8q5RWfOyG7Rzz+zW0RkAdg5IiLKrgsX5JVomm5Rhw7AkiUsiogsBDtHRERZlZICTJkC1K2r3y3asoWFEZEFYeeIiCgrLlyQc4vOnpXjjh1lt8jNzahhEZHhsXNERPQqKSnA5MmyW3T2LFCkCLBuHbB5MwsjIgvFzhERUWbOn5fdonPn5LhTJ2DxYhZFRBaOnSMiorR0u0Xnzr3sFoWGsjAiygfYOSIi0nXunLwSTdMtev992S0qXtyYURFRHmLniIgIAJKTgUmTgHr1ZGFUtCjwyy/Apk0sjIjyGXaOiIjOnZNzi86fl+OAAGDRIhZFRPkUO0dElH8lJwMTJ8pu0fnzslv066/Axo0sjIjyMXaOiCh/YreIiDLBzhER5S/JycCECewWEVGm2Dkiovzj7FnZLbpwQY4/+EB2i4oVM2pYRGRa2DkiIsuXnAyMHw/Ury8Lo7feAtavl90iFkZElAY7R0Rk2c6ckd2iixfluHNnYOFCFkVElCl2jojIMiUnA+PGyW7RxYsvu0UbNrAwIqJXYueIiCzP6dPyLteablGXLsCCBYCrq3HjIiKzwM4REVmOpCTgm2+ABg1kYeTqKjtFv/3GwoiIsoydIyKyDKdPy7lFly7JMbtFRPSG2DkiIvOm2y26dIndIiLKMXaOiMhsuVy/DuuvvwYuX5YLunYF5s9nUUREOcLiiIjMT1ISrCZMgO/s2VCo1fLqs0WL5E0diYhyyKRPq6lUKowbNw5eXl4oWLAgvL29MWXKFAghtNsIITB+/Hi4u7ujYMGC8PPzw7Vr14wYNRHlqlOngDp1oJw5E1ZqNdRdusjOEQsjIjIQky6OZs6cicWLF2PBggW4cuUKZs6ciVmzZmH+/PnabWbNmoV58+ZhyZIliIyMhIODA/z9/ZGYmGjEyInI4JKSgK++At55B7h8GaJYMZwYMwaqn3+W9zAiIjIQky6Ojh49io4dO6Jt27YoU6YMOnfujFatWuHEiRMAZNcoODgY33zzDTp27Ijq1atj9erVuHfvHrZs2WLc4InIcE6eBGrXBqZPB1QqoFs3pJ47h/sNGxo7MiKyQCY956hRo0ZYtmwZ/v77b1SoUAHnz5/H4cOHMXfuXADAjRs3EBUVBT8/P+1jnJ2d0aBBAxw7dgzdunXLcL9JSUlISkrSjmNjYwEAKSkpSElJycWMDE8Tr7nFbSjM38LzT0yE1ZQpsPruOyjUaohixaCaPx/i/fctP/fXYP7MX/ff/Ca381cI3Qk8JkatVuOrr77CrFmzoFQqoVKpMG3aNIwdOxaA7Cz5+Pjg3r17cHd31z6uS5cuUCgU+O233zLc78SJEzFp0qR0y9etWwd7e/vcSYaIssXl779Ra/58ON25AwC406QJLn72GVKcnIwcGRGZioSEBHTv3h0xMTFwMuDvBpPuHK1fvx5r167FunXrUKVKFZw7dw5BQUHw8PBAYGDgG+937NixGD58uHYcGxsLT09PtGrVyqBPbl5ISUlBeHg4WrZsCRsbG2OHk+eYvwXmn5gIq8mTYTV3ruwWFS8O1YIFcOvYEW46m1lk7tnA/Jk/8w9H8+bNc2X/Jl0cjRo1Cl9++aX29Fi1atVw69YtTJ8+HYGBgXBzk78qHzx4oNc5evDgAWrWrJnpfm1tbWFra5tuuY2Njdm+yMw5dkNg/haS/4kT8i7XV67IcffuUMybB+uiRTN9iMXk/oaYP/PP7/nnhixPyL53716uBPAqCQkJsLLSD1GpVEKtVgMAvLy84Obmhr1792rXx8bGIjIyEg05UZPIfCQmAl9+CTRsKAuj4sWBzZuBtWuBVxRGRES5IcvFUZUqVbBu3brcjCWd9u3bY9q0afjjjz9w8+ZNbN68GXPnzsX7778PAFAoFAgKCsLUqVOxdetWXLx4ET179oSHhwc6deqUp7ES0RuKjARq1QJmzgTUaqBHD+DPPwH+DBORkWT5tNq0adPQv39/bN68GUuXLkWRIkVyMy4AwPz58zFu3DgMGjQIDx8+hIeHB/r374/x48drtxk9ejTi4+PRr18/REdHo3HjxggLC4OdnV2ux0dEOZCYCEyYAMyZI4siNzdgyRKgY0djR0ZE+VyWO0eDBg3ChQsX8OTJE1SuXBnbtm3LzbgAAIUKFUJwcDBu3bqFFy9e4J9//sHUqVNRoEAB7TYKhQKTJ09GVFQUEhMTsWfPHlSoUCHXYyOiHDh+XHaLZs2ShdHHH8u7XLMwIiITkK0J2V5eXti3bx8WLFiAgIAAVKpUCdbW+rs4c+aMQQMkIgvy4oXsFn333ctu0dKlQIcOxo6MiEgr21er3bp1C6GhoShcuDA6duyYrjgiIsrQ8ePySrSrV+X4k0+A4GAgD07RExFlR7Yqm+XLl2PEiBHw8/PD5cuX4erqmltxEZGlePECGD8emDv3Zbdo2TKgfXtjR0ZElKEsF0etW7fGiRMnsGDBAvTs2TM3YyIiS3HsGNC798tuUc+ewPffs1tERCYty8WRSqXChQsXULJkydyMh4gswYsXwLhxslskBODuLucWsVtERGYgy8VReHh4bsZBRJbi6FHZLfr7bznu2VPOLSpc2KhhERFlVZYv5ScieqUXL4ARI4DGjWVh5OEBbNsG/PQTCyMiMiu81IyIcu7IEeDTT192iwID5dwiFkVEZIbYOSKiN5eQAAwfDvj6vuwWbd8OhISwMCIis8XOERG9mSNH5Nyia9fkuFcvOQGbRRERmTkWR0QWRKUCIiKA+/flBWK+voBSaeCDJCQA33wjJ1kLIbtFy5cD771n4AMRERkHiyMiCxEaCgwbBvz338tlJUsCP/wABAQY6CCHD8tu0fXrcty7t+wWubgY6ABERMbHOUdEFmDbNqBzZ/3CCADu3pXLQ0NzeICEBOCLL4AmTWRhVKIE8McfwMqVLIyIyOKwOCKyAGPGyDNcaWmWBQXJU25vJCICqFHj5Wm0Tz8FLl3iaTQislgsjogswN27ma8TArhzR9Y42ZKQIKuqpk1lt6hkSWDnTuDHH9ktIiKLxjlHRPnE/fvZ2DgiQs4n+ucfOf70Uzm3yNk5V2IjIjIl7BwR5RPu7lnYKD5ezupu2lQWRrrdIhZGRJRPsDgisgAlSgAKRcbrFArA01Ne1v9Khw7JuUXz5slzcX36yLlFrVsbPF4iIlPG4ojIAsycKf9NWyBpxsHBr7jfUXw8MHSofrcoLAxYsYLdIiLKl1gcEVmA9u2BjRtlB0lXyZJyeab3OTp4EKheHZg/X44/+0x2i/z9czVeIiJTxgnZRBYiIADo2DGLd8iOjwe+/BJYsECOPT1lp6hVqzyNmYjIFLE4IrIgSiXQrNlrNjpwQM4n+vdfOe7bF5g9m6fQiIj+j6fViPKL58+Bzz8HmjeXhZGnJ7BrF7BsGQsjIiId7BwR5QcHDsh7Fd24Icf9+slukZOTUcMiIjJF7BwRWbLnz4EhQ2S36MYNoFQpYPduYOlSFkZERJlg54jIUu3fL+cWsVtERJQt7BwRWZrnz4HBg4F3333ZLQoPZ7eIiCiL2DkisiT79slu0c2bcjxgADBrFlCokFHDIiIyJ+wcEVmC58+BQYOAFi1kYVS6NLBnD7B4MQsjIqJsYnFEZObeunAB1rVry0IIkN2iixdloURERNnG02pE5iouDlYjR8Jn2TI5Ll0a+PFHFkVERDnEzhGROdq7F6hWDcr/F0aq/v3ZLSIiMhAWR0TmJC5Onjbz8wNu3YIoUwZHJk+Gev58zi0iIjIQFkdE5mLPHqBqVXlJPgAMGoTUM2fwuHp148ZFRGRhWBwRmbrYWKB/f6BlS+D2baBMGXnJ/sKFgKOjsaMjIrI4LI6ITFl4OFCtmvxwWEDe3PHiRflxIERElCt4tRrRK6hUQEQEcP8+4O4O+PoCSmUeHDg2Fhg16mVR5OUlr0RjUURElOtYHBFlIjQUGDYM+O+/l8tKlgR++AEICMjFA4eHA599Jk+hAfKDY6dP5yk0IqI8wtNqRBkIDQU6d9YvjADg7l25PDQ0Fw4aGys/HLZVK1kYlS0rPzx2/nwWRkREeYjFEVEaKpXsGAmRfp1mWVCQ3M5gdu+WV6ItXy7HQ4YAFy4AzZoZ8CBERJQVLI6I0oiISN8x0iUEcOeO3C7HYmKAvn0Bf3+507JlgQMHZLfIwcEAByAiouxicUSUxv37ht0uU7t2yW7RihVyPHSo7BY1bZrDHRMRUU5wQjZRGu7uht0unZgYYMQIefUZILtFq1YBTZq84Q6JiMiQ2DkiSsPXV16VplBkvF6hADw95XbZFhYmu0WawkjTLWJhRERkMlgcEaWhVMrL9YH0BZJmHByczfsdRUcDffoAbdrICU3e3sDBg/JAnFtERGRSWBwRZSAgANi4EShRQn95yZJyebbuc7Rzp+wWrVwpq6thw9gtIiIyYZxzRJSJgACgY8cc3CE7OhoYPlzOJwKAcuVkgfRG5+OIiCivsDgiegWl8g1vNbRjh7yh4927L7tF06YB9vaGDpGIiAyMxRGRIUVHA198AYSEyHG5crJz1LixMaMiIqJs4JwjIkPZsQOoUkUWRgqFLJLOn2dhRERkZtg5IsqpZ89kIfTTT3JcvrycW8SiiIjILLFzRJQTf/whr0T76SfZLRo+HDh3joUREZEZY+eI6E2k7RZVqCDnFjVqZNy4iIgox9g5IsquzLpFLIyIiCwCO0dEWfXsGRAUBKxeLcfsFhERWSR2joiyYts2eSXa6tWyWzRyJLtFREQWip0jold59kzewHHNGjmuUEFeqt+woVHDIiKi3MPOEVFmNN2iNWsAK6uX3SIWRkREFs3ki6O7d+/i448/RtGiRVGwYEFUq1YNp06d0q4XQmD8+PFwd3dHwYIF4efnh2vXrhkxYjJ7T58Cn3wCdOggP1Tt7beBw4eB2bOBggWNHR0REeUyky6Onj17Bh8fH9jY2GDnzp34888/8d1336Fw4cLabWbNmoV58+ZhyZIliIyMhIODA/z9/ZGYmGjEyMlsbd0qu0U//yy7RaNGAWfPsltERJSPmPSco5kzZ8LT0xOrNJ9qDsDLy0v7fyEEgoOD8c0336Bjx44AgNWrV6N48eLYsmULunXrlucxk2GpVEBEhGzguLvLD7RXKnPhQE+fAkOHAmvXynHFivJKtHfeyYWDERGRKTPp4mjr1q3w9/fHhx9+iIMHD6JEiRIYNGgQ+vbtCwC4ceMGoqKi4Ofnp32Ms7MzGjRogGPHjmVaHCUlJSEpKUk7jo2NBQCkpKQgJSUlFzMyPE285hZ3VmzbBowZIz/YXqNECWDmTKB9ezk2RP6KrVuhHDwYigcPIKysoP7iC6gnTADs7AATf14t+fv/Ovk5d4D5M3/mr/uvoSmEECJX9mwAdnZ2AIDhw4fjww8/xMmTJzFs2DAsWbIEgYGBOHr0KHx8fHDv3j24u7trH9elSxcoFAr89ttvGe534sSJmDRpUrrl69atg729fe4kQybHJjYW1X78EZ4HDwIA4kqWxNmhQ/GsQgUjR0ZERFmRkJCA7t27IyYmBk5OTgbbr0kXRwUKFEDdunVx9OhR7bKhQ4fi5MmTOHbs2BsXRxl1jjw9PfH48WODPrl5ISUlBeHh4WjZsiVsbGyMHY5BqFRAtWr6HSNdCoXsIF24AKjVb5Z/um7R8OFQjx8vu0VmxBK//1mVn3MHmD/zZ/7h4eFo0KAB3N3dDV4cmfRpNXd3d1SuXFlvWaVKlbBp0yYAgJubGwDgwYMHesXRgwcPULNmzUz3a2trC1tb23TLbWxszPZFZs6xp3XkCHD9+qu3uXYNOH4c8PGR4yzn/+SJnFu0bp0cV6oExapVUDZogNyYypRXLOn7n135OXeA+TN/5p8bTPpqNR8fH1y9elVv2d9//43SpUsDkJOz3dzcsHfvXu362NhYREZGoiGvLjJb9+8bdjutLVvklWjr1skr0b78EjhzBmjQILshEhGRBTPpztEXX3yBRo0a4dtvv0WXLl1w4sQJLFu2DMuWLQMAKBQKBAUFYerUqShfvjy8vLwwbtw4eHh4oFOnTsYNnt6YThPQINvhyRPg88+BX36R48qV5ZVo9eu/UXxERGTZTLo4qlevHjZv3oyxY8di8uTJ8PLyQnBwMHr06KHdZvTo0YiPj0e/fv0QHR2Nxo0bIywsTDuZm8yPry9QsqScc5TRjDiFQq739QXU6tfsbPNmYMAA4OFD2S0aPRrQXIlGRESUAZMujgCgXbt2aNeuXabrFQoFJk+ejMmTJ+dhVJSblErghx+Azp1lIaRbICkU8t/gYLldpsXR48eyW/Trr3JcubL8TLR69XIxciIisgQmPeeI8q+AAGDjRnlVmq6SJeXygIBXPDg0VM4t+vVXWUF99ZWcW8TCiIiIssDkO0eUfwUEAB07ZuMO2Y8fA0OGAJpbOFSpIrtFdevmVchERGQBWByRSVMqgWbNXr+dIjRUnkZ79Eg+aMwYYPx4IINbNhAREb0KiyMyb48eoc6cObA+fFiO2S0iIqIc4pwjMl+bNsG6Zk2UPHwYQqkEvv4aOH2ahREREeUIO0dkfh49knOL1q+HAkBsqVIouH49bHgzRyIiMgAWR2ReNm4EBg3Szi1SjR6Ng7Vro3Xt2saOjIiILARPq5F5ePQI6NIF+PBD+f9q1YDISKgnTYI6H3+uEBERGR6LIzJ9GzbImzhu2CCvRBs3Djh1CqhTx9iRERGRBeJpNTJdDx8CgwfLU2mA7BaFhAA8hUZERLmInSMyTevXy8vyN24ErK1fdotYGBERUS5j54hMS9puUfXqsltUq5ZRwyIiovyDnSMyDULIj/2oXPllt2j8eODkSRZGRESUp9g5IuN78EB2izZtkuMaNYBVq1gUERGRUbBzRMYjBPDrr3Ju0aZNsls0YQJw4gQLIyIiMhp2jsg4HjyQN3MMDZXjGjXk3KKaNY0ZFRERETtHlMd0u0WhobJbNHGi7BaxMCIiIhPAzhHlnago2S3avFmOa9aU3aIaNYwZFRERkR52jij3CQH88ovsFm3eLLtFkybJbhELIyIiMjHsHFHuiooCBg4EtmyR41q15JVoLIqIiMhEsXNEuUMIYN062S3asgWwsQEmTwYiI1kYERGRSWPniAwvKgoYMAD4/Xc5rlVLzi2qXt2oYREREWUFO0dkOEIAa9fKu1z//rvsFk2ZIrtFLIyIiMhMsHNEhnH/vuwWbd0qx7Vry7lFLIqIiMjMsHNEOSME8PPPcm7R1q2yWzR1KnD8OAsjIiIyS+wc0ZvLqFsUEgJUq2bUsIiIiHKCnSPKPiGANWvk3KK03SIWRkREZObYOaLsuXcP6N8f2L5djuvUkd2iqlWNGhYREZGhsHNEWSMEsHq1nFu0fbvsFk2bJrtFLIyIiMiCsHNEr5e2W1S3rrwSjUURERFZIHaOKHNpu0UFCgDffgscO8bCiIiILBY7R5Sxu3dlt+iPP+S4bl05t6hKFaOGRURElNvYOSJ9QgA//SSLoD/+kN2i6dNlt4iFERER5QPsHNFLd+8C/foBO3bIcb16sltUubJRwyIiIspL7ByR7BZpTpnt2CG7RTNmAEePsjAiIqJ8h52j/C5tt6h+fXklGosiIiLKp9g5yq+EkEVQ2m7RkSMsjIiIKF9j5yg/+u8/oG9fICxMjtktIiIi0mLnKD8RAli5UnaLwsIAW1tg1ix2i4iIiHSwc5Rf3Lkj5xZpukUNGshuUaVKxo2LiIjIxLBzZOmEAH78Ud7ROm23iIURERFROuwcWbI7d+Tcol275Pidd2S3qGJF48ZFRERkwtg5skRCACtWyLlFu3bJbtHs2cDhwyyMiIiIXoOdI0tz+7bsFu3eLccNG8pJ2CyKiIiIsoSdI0shBLB8uZxbtHs3YGcHzJkDRESwMCIiIsoGdo4sQMFHj6Bs2xbYs0cuaNhQzi16+23jBkZERGSGWByZMyGgWLECzUeMgNWLF7JbNG0aMGwYoFQaOzoiIiKzxOLIXN26BXz2Gaz/3y1SN2wIK3aLiIiIcoxzjsyNEMCyZUC1asCePRB2drj46adQ7dvHwoiIiMgAWByZk1u3gFatgP79gbg4wMcHqadO4d8OHXgajYiIyEBYHJkDIYClS+WVaHv2yLlFc+cCBw8CFSoYOzoiIiKLwjlHpu7mTeCzz4C9e+XYx0deiVa+vByr1UYLjYiIyBKxc2SqhACWLJFzi/buBQoWBIKDZbdIUxgRERGRwbFzZIpu3gT69AH27ZPjxo3lXa5ZFBEREeU6do5MiVoNLF4s5xbt28duERERkRGwc2QqbtyQ3aL9++XY11d2i8qVM25cRERE+Qw7R8amVgOLFsm5Rfv3y27RDz8ABw6wMCIiIjICdo6MKW23qEkT4McfWRQREREZkVl1jmbMmAGFQoGgoCDtssTERAwePBhFixaFo6MjPvjgAzx48MB4QWaFWg0sXPiyW2RvD8ybJ//PwoiIiMiozKY4OnnyJJYuXYrq1avrLf/iiy+wbds2bNiwAQcPHsS9e/cQEBBgpCiz4N9/gRYtgCFDgPh42S26cAH4/HPAymy+HURERBbLLN6Nnz9/jh49emD58uUoXLiwdnlMTAx+/PFHzJ07F++++y7q1KmDVatW4ejRozh+/LgRI86AWg0sWCC7RQcOyG7R/PmyW+TtbezoiIiI6P/MYs7R4MGD0bZtW/j5+WHq1Kna5adPn0ZKSgr8/Py0yypWrIhSpUrh2LFjeOeddzLcX1JSEpKSkrTj2NhYAEBKSgpSUlIMn8C//0LZrx+sDh0CAKibNIFq6VJZFKlU8usNaeLNlbjNAPPPv/nn59wB5s/8mb/uv4Zm8sXRr7/+ijNnzuDkyZPp1kVFRaFAgQJwcXHRW168eHFERUVlus/p06dj0qRJ6Zbv3r0b9vb2OY5ZS62G186dqLx6NaySkpBqa4s/AwNxo3Vr4OpV+WUg4eHhBtuXOWL++Tf//Jw7wPyZf/7Of7/mgiYDM+ni6M6dOxg2bBjCw8NhZ2dnsP2OHTsWw4cP145jY2Ph6emJVq1awcnJyTAHiY6GsnPnl92ipk0hli5FpbJlUckwRwAgq+bw8HC0bNkSNjY2BtyzeWD++Tf//Jw7wPyZP/MPDw9H8+bNc2X/Jl0cnT59Gg8fPkTt2rW1y1QqFQ4dOoQFCxZg165dSE5ORnR0tF736MGDB3Bzc8t0v7a2trC1tU233MbGxnAvsqJFAWtrwMEBmDkTVgMHwioXJ1wbNHYzxPzzb/75OXeA+TN/5p8bTLo4atGiBS5evKi3rHfv3qhYsSLGjBkDT09P2NjYYO/evfjggw8AAFevXsXt27fRsGFDY4T8kpUVsGoVkJoKlC1r3FiIiIgoy0y6OCpUqBCqVq2qt8zBwQFFixbVLu/Tpw+GDx+OIkWKwMnJCZ9//jkaNmyY6WTsPFWqlLEjICIiomwy6eIoK77//ntYWVnhgw8+QFJSEvz9/bFo0SJjh0VERERmyuyKowMHDuiN7ezssHDhQixcuNA4AREREZFFMYubQBIRERHlFRZHRERERDpYHBERERHpYHFEREREpIPFEREREZEOFkdEREREOlgcEREREelgcURERESkg8URERERkQ4WR0REREQ6WBwRERER6WBxRERERKSDxRERERGRDhZHRERERDpYHBERERHpYHFEREREpIPFEREREZEOFkdEREREOlgcEREREelgcURERESkg8URERERkQ4WR0REREQ6WBwRERER6WBxRERERKSDxRERERGRDhZHRERERDqsjR2ApVKpgIgI4P59wN0d8PUFlEpjR0VERESvw+IoF4SGAsOGAf/993JZyZLADz8AAQHGi4uIiIhej6fVDCw0FOjcWb8wAoC7d+Xy0FDjxEVERERZw+LIgFQq2TESIv06zbKgILkdERERmSYWRwYUEZG+Y6RLCODOHbkdERERmSYWRwZ0/75htyMiIqK8x+LIgNzdDbsdERER5T0WRwbk6yuvSlMoMl6vUACennI7IiIiMk0sjgxIqZSX6wPpCyTNODiY9zsiIiIyZSyODCwgANi4EShRQn95yZJyOe9zREREZNp4E8hcEBAAdOzIO2QTERGZIxZHuUSpBJo1M3YURERElF08rUZERESkg8URERERkQ4WR0REREQ6WBwRERER6WBxRERERKSDxRERERGRDhZHRERERDpYHBERERHpYHFEREREpIN3yAYghAAAxMbGGjmS7EtJSUFCQgJiY2NhY2Nj7HDyHPPPv/nn59wB5s/8mX9CQgLi4uIAvHwfNxQWR4D2yfX09DRyJERERJRdcXFxcHZ2Ntj+FMLQ5ZYZUqvVuHfvHgoVKgSFQmHscLIlNjYWnp6euHPnDpycnIwdTp5j/vk3//ycO8D8mT/z9/T0xO3bt6FQKODh4QErK8PNFGLnCICVlRVKlixp7DByxMnJKV/+gGgw//ybf37OHWD+zD9/5+/s7Jwr+XNCNhEREZEOFkdEREREOlgcmTlbW1tMmDABtra2xg7FKJh//s0/P+cOMH/mz/xzM39OyCYiIiLSwc4RERERkQ4WR0REREQ6WBwRERER6WBxRERERKSDxZEZmD59OurVq4dChQqhWLFi6NSpE65evaq3TWJiIgYPHoyiRYvC0dERH3zwAR48eGCkiHPXjBkzoFAoEBQUpF1m6fnfvXsXH3/8MYoWLYqCBQuiWrVqOHXqlHa9EALjx4+Hu7s7ChYsCD8/P1y7ds2IERuOSqXCuHHj4OXlhYIFC8Lb2xtTpkzR+ywlS8r/0KFDaN++PTw8PKBQKLBlyxa99VnJ9enTp+jRowecnJzg4uKCPn364Pnz53mYxZt5Ve4pKSkYM2YMqlWrBgcHB3h4eKBnz564d++e3j7MNXfg9d97XQMGDIBCoUBwcLDeckvP/8qVK+jQoQOcnZ3h4OCAevXq4fbt29r1hnovYHFkBg4ePIjBgwfj+PHjCA8PR0pKClq1aoX4+HjtNl988QW2bduGDRs24ODBg7h37x4CAgKMGHXuOHnyJJYuXYrq1avrLbfk/J89ewYfHx/Y2Nhg586d+PPPP/Hdd9+hcOHC2m1mzZqFefPmYcmSJYiMjISDgwP8/f2RmJhoxMgNY+bMmVi8eDEWLFiAK1euYObMmZg1axbmz5+v3caS8o+Pj0eNGjWwcOHCDNdnJdcePXrg8uXLCA8Px/bt23Ho0CH069cvr1J4Y6/KPSEhAWfOnMG4ceNw5swZhIaG4urVq+jQoYPeduaaO/D6773G5s2bcfz4cXh4eKRbZ8n5//PPP2jcuDEqVqyIAwcO4MKFCxg3bhzs7Oy02xjsvUCQ2Xn48KEAIA4ePCiEECI6OlrY2NiIDRs2aLe5cuWKACCOHTtmrDANLi4uTpQvX16Eh4eLpk2bimHDhgkhLD//MWPGiMaNG2e6Xq1WCzc3NzF79mztsujoaGFrayt++eWXvAgxV7Vt21Z8+umnessCAgJEjx49hBCWnT8AsXnzZu04K7n++eefAoA4efKkdpudO3cKhUIh7t69m2ex51Ta3DNy4sQJAUDcunVLCGE5uQuRef7//fefKFGihLh06ZIoXbq0+P7777XrLD3/rl27io8//jjTxxjyvYCdIzMUExMDAChSpAgA4PTp00hJSYGfn592m4oVK6JUqVI4duyYUWLMDYMHD0bbtm318gQsP/+tW7eibt26+PDDD1GsWDHUqlULy5cv166/ceMGoqKi9PJ3dnZGgwYNLCL/Ro0aYe/evfj7778BAOfPn8fhw4fRpk0bAJafv66s5Hrs2DG4uLigbt262m38/PxgZWWFyMjIPI85N8XExEChUMDFxQWA5eeuVqvxySefYNSoUahSpUq69Zacv1qtxh9//IEKFSrA398fxYoVQ4MGDfROvRnyvYDFkZlRq9UICgqCj48PqlatCgCIiopCgQIFtL8gNIoXL46oqCgjRGl4v/76K86cOYPp06enW2fp+f/7779YvHgxypcvj127dmHgwIEYOnQofvrpJwDQ5li8eHG9x1lK/l9++SW6deuGihUrwsbGBrVq1UJQUBB69OgBwPLz15WVXKOiolCsWDG99dbW1ihSpIhFPR+JiYkYM2YMPvroI+0Hj1p67jNnzoS1tTWGDh2a4XpLzv/hw4d4/vw5ZsyYgdatW2P37t14//33ERAQgIMHDwIw7HuBtaECp7wxePBgXLp0CYcPHzZ2KHnmzp07GDZsGMLDw/XOLecXarUadevWxbfffgsAqFWrFi5duoQlS5YgMDDQyNHlvvXr12Pt2rVYt24dqlSpgnPnziEoKAgeHh75In9KLyUlBV26dIEQAosXLzZ2OHni9OnT+OGHH3DmzBkoFApjh5Pn1Go1AKBjx4744osvAAA1a9bE0aNHsWTJEjRt2tSgx2PnyIwMGTIE27dvx/79+1GyZEntcjc3NyQnJyM6Olpv+wcPHsDNzS2PozS806dP4+HDh6hduzasra1hbW2NgwcPYt68ebC2tkbx4sUtOn93d3dUrlxZb1mlSpW0V2hockx7RYal5D9q1Cht96hatWr45JNP8MUXX2i7iJaev66s5Orm5oaHDx/qrU9NTcXTp08t4vnQFEa3bt1CeHi4tmsEWHbuERERePjwIUqVKqX9PXjr1i2MGDECZcqUAWDZ+b/11luwtrZ+7e9CQ70XsDgyA0IIDBkyBJs3b8a+ffvg5eWlt75OnTqwsbHB3r17tcuuXr2K27dvo2HDhnkdrsG1aNECFy9exLlz57RfdevWRY8ePbT/t+T8fXx80t264e+//0bp0qUBAF5eXnBzc9PLPzY2FpGRkRaRf0JCAqys9H9VKZVK7V+Slp6/rqzk2rBhQ0RHR+P06dPabfbt2we1Wo0GDRrkecyGpCmMrl27hj179qBo0aJ66y05908++QQXLlzQ+z3o4eGBUaNGYdeuXQAsO/8CBQqgXr16r/xdaND3wmxN3yajGDhwoHB2dhYHDhwQ9+/f134lJCRotxkwYIAoVaqU2Ldvnzh16pRo2LChaNiwoRGjzl26V6sJYdn5nzhxQlhbW4tp06aJa9euibVr1wp7e3vx888/a7eZMWOGcHFxEb///ru4cOGC6Nixo/Dy8hIvXrwwYuSGERgYKEqUKCG2b98ubty4IUJDQ8Vbb70lRo8erd3GkvKPi4sTZ8+eFWfPnhUAxNy5c8XZs2e1V2RlJdfWrVuLWrVqicjISHH48GFRvnx58dFHHxkrpSx7Ve7JycmiQ4cOomTJkuLcuXN6vwuTkpK0+zDX3IV4/fc+rbRXqwlh2fmHhoYKGxsbsWzZMnHt2jUxf/58oVQqRUREhHYfhnovYHFkBgBk+LVq1SrtNi9evBCDBg0ShQsXFvb29uL9998X9+/fN17QuSxtcWTp+W/btk1UrVpV2NraiooVK4ply5bprVer1WLcuHGiePHiwtbWVrRo0UJcvXrVSNEaVmxsrBg2bJgoVaqUsLOzE2XLlhVff/213huiJeW/f//+DH/eAwMDhRBZy/XJkyfio48+Eo6OjsLJyUn07t1bxMXFGSGb7HlV7jdu3Mj0d+H+/fu1+zDX3IV4/fc+rYyKI0vP/8cffxTlypUTdnZ2okaNGmLLli16+zDUe4FCCJ3bzBIRERHlc5xzRERERKSDxRERERGRDhZHRERERDpYHBERERHpYHFEREREpIPFEREREZEOFkdEREREOlgcEREREelgcUREFkWlUqFRo0YICAjQWx4TEwNPT098/fXXRoqMiMwF75BNRBbn77//Rs2aNbF8+XL06NEDANCzZ0+cP38eJ0+eRIECBYwcIRGZMhZHRGSR5s2bh4kTJ+Ly5cs4ceIEPvzwQ5w8eRI1atQwdmhEZOJYHBGRRRJC4N1334VSqcTFixfx+eef45tvvjF2WERkBlgcEZHF+uuvv1CpUiVUq1YNZ86cgbW1tbFDIiIzwAnZRGSxVq5cCXt7e9y4cQP//fefscMhIjPBzhERWaSjR4+iadOm2L17N6ZOnQoA2LNnDxQKhZEjIyJTx84REVmchIQE9OrVCwMHDkTz5s3x448/4sSJE1iyZImxQyMiM8DOERFZnGHDhmHHjh04f/487O3tAQBLly7FyJEjcfHiRZQpU8a4ARKRSWNxREQW5eDBg2jRogUOHDiAxo0b663z9/dHamoqT68R0SuxOCIiIiLSwTlHRERERDpYHBERERHpYHFEREREpIPFEREREZEOFkdEREREOlgcEREREelgcURERESkg8URERERkQ4WR0REREQ6WBwRERER6WBxRERERKSDxRERERGRjv8BfN0JNstrEkcAAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["拟合直线的方程: y = 0.62x + 31.79\n", "拟合优度 (R^2): 0.96\n", "被剔除的离群值 x:\n", "Empty DataFrame\n", "Columns: [x, y]\n", "Index: []\n", "被剔除的离群值 y:\n", "Empty DataFrame\n", "Columns: [x, y]\n", "Index: []\n"]}], "source": ["df = result.copy()\n", "df.columns = ['x','y']\n", "\n", "# 计算均值和标准差\n", "mean_x = df['x'].mean()\n", "std_dev_x = df['x'].std()\n", "mean_y = df['y'].mean()\n", "std_dev_y = df['y'].std()\n", "\n", "# 定义阈值范围\n", "threshold_x = 3 * std_dev_x\n", "threshold_y = 3 * std_dev_y\n", "\n", "# 应用3σ原则，剔除离群值\n", "df_filtered = df[(np.abs(df['x'] - mean_x) <= threshold_x) & (np.abs(df['y'] - mean_y) <= threshold_y)]\n", "\n", "# 使用过滤后的数据进行最小二乘法拟合\n", "x_filtered = df_filtered['x'].values\n", "y_filtered = df_filtered['y'].values\n", "slope, intercept, r_value, p_value, std_err = stats.linregress(x_filtered, y_filtered)\n", "y_pred = intercept + slope * x_filtered\n", "\n", "# 绘制原始数据和拟合直线\n", "\n", "fig,ax = plt.subplots()\n", "\n", "\n", "\n", "ax.scatter(x_filtered, y_filtered, color='blue', label='Filtered Data')  # 绘制过滤后的数据散点图\n", "ax.plot(x_filtered, y_pred, color='red', label='Fitted line')  # 绘制拟合直线\n", "plt.xlabel('X')\n", "plt.ylabel('Y')\n", "plt.title('Linear Regression Fit with <PERSON><PERSON> Removal (3σ Rule)')\n", "plt.legend()\n", "plt.grid(True)\n", "# 在图中添加拟合直线的方程\n", "equation = f'y = {slope:.2f}σ + {intercept:.2f}'\n", "\n", "\n", "# r=69.4\n", "# o=(74.4,0)\n", "# circle = patches.Circle(o, r, edgecolor='blue', facecolor='none')\n", "# ax.add_patch(circle)\n", "# ax.set_xlim(o[0] - r - 1, o[0] + r + 1)\n", "# ax.set_ylim(0, o[1] + r + 1)\n", "# ax.set_aspect('equal')\n", "\n", "\n", "\n", "plt.show()\n", "\n", "# 计算拟合优度 (R^2)\n", "r_squared = r_value**2\n", "print(f'拟合直线的方程: y = {slope:.2f}x + {intercept:.2f}')\n", "print(f'拟合优度 (R^2): {r_squared:.2f}')\n", "\n", "# 输出剔除的离群值\n", "outliers_x = df[~((np.abs(df['x'] - mean_x) <= threshold_x))]\n", "outliers_y = df[~((np.abs(df['y'] - mean_y) <= threshold_y))]\n", "print(f'被剔除的离群值 x:\\n{outliers_x}')\n", "print(f'被剔除的离群值 y:\\n{outliers_y}')\n"]}, {"cell_type": "code", "execution_count": 132, "id": "c832d44f", "metadata": {"ExecuteTime": {"end_time": "2024-06-30T01:50:35.351544Z", "start_time": "2024-06-30T01:50:35.166623Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Filtered data points: 6\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["拟合直线的方程: y = 0.62x + 31.79\n", "拟合优度 (R^2): 0.96\n", "被剔除的离群值 x:\n", "Empty DataFrame\n", "Columns: [x, y]\n", "Index: []\n", "被剔除的离群值 y:\n", "Empty DataFrame\n", "Columns: [x, y]\n", "Index: []\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "from scipy import stats\n", "import matplotlib.pyplot as plt\n", "import matplotlib.patches as patches\n", "\n", "# 示例数据，替换为你的实际数据\n", "df = result.copy()\n", "df.columns = ['x', 'y']\n", "\n", "# 计算均值和标准差\n", "mean_x = df['x'].mean()\n", "std_dev_x = df['x'].std()\n", "mean_y = df['y'].mean()\n", "std_dev_y = df['y'].std()\n", "\n", "# 定义阈值范围\n", "threshold_x = 3 * std_dev_x\n", "threshold_y = 3 * std_dev_y\n", "\n", "# 应用3σ原则，剔除离群值\n", "df_filtered = df[(np.abs(df['x'] - mean_x) <= threshold_x) & (np.abs(df['y'] - mean_y) <= threshold_y)]\n", "\n", "# 使用过滤后的数据进行最小二乘法拟合\n", "x_filtered = df_filtered['x'].values\n", "y_filtered = df_filtered['y'].values\n", "\n", "# 检查过滤后的数据点数量\n", "print(f\"Filtered data points: {len(x_filtered)}\")\n", "\n", "if len(x_filtered) < 2:\n", "    print(\"Not enough data points after filtering to perform linear regression.\")\n", "else:\n", "    slope, intercept, r_value, p_value, std_err = stats.linregress(x_filtered, y_filtered)\n", "    y_pred = intercept + slope * x_filtered\n", "\n", "    # 绘制原始数据和拟合直线\n", "    fig, ax = plt.subplots()\n", "    \n", "    for i in list_r_o:\n", "        r = i[1]\n", "        o = (i[0], 0)\n", "        circle = patches.Arc(o, 2*r, 2*r, theta1=0, theta2=180, edgecolor='blue')\n", "        ax.add_patch(circle)\n", "        ax.set_xlim(o[0] - r - 1, o[0] + r + 1)\n", "        ax.set_ylim(0, o[1] + r + 1)\n", "        ax.set_aspect('equal')\n", "\n", "    ax.scatter(x_filtered, y_filtered, color='blue', label='Filtered Data')  # 绘制过滤后的数据散点图\n", "    ax.plot(x_filtered, y_pred, color='red', label='Fitted line')  # 绘制拟合直线\n", "    plt.xlabel('X')\n", "    plt.ylabel('Y')\n", "    plt.title('Linear Regression Fit with <PERSON><PERSON> Removal (3σ Rule)')\n", "    plt.legend()\n", "    plt.grid(True)\n", "\n", "    # 在图中添加拟合直线的方程\n", "    equation = f'y = {slope:.2f}x + {intercept:.2f}'\n", "    plt.text(0.05, 0.95, equation, transform=ax.transAxes, fontsize=12, verticalalignment='top')\n", "\n", "    plt.show()\n", "\n", "    # 计算拟合优度 (R^2)\n", "    r_squared = r_value**2\n", "    print(f'拟合直线的方程: y = {slope:.2f}x + {intercept:.2f}')\n", "    print(f'拟合优度 (R^2): {r_squared:.2f}')\n", "\n", "    # 输出剔除的离群值\n", "    outliers_x = df[~((np.abs(df['x'] - mean_x) <= threshold_x))]\n", "    outliers_y = df[~((np.abs(df['y'] - mean_y) <= threshold_y))]\n", "    print(f'被剔除的离群值 x:\\n{outliers_x}')\n", "    print(f'被剔除的离群值 y:\\n{outliers_y}')\n"]}, {"cell_type": "code", "execution_count": 136, "id": "0eab8494", "metadata": {"ExecuteTime": {"end_time": "2024-06-30T01:53:39.903945Z", "start_time": "2024-06-30T01:53:39.716903Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Filtered data points: 6\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["拟合直线的方程: y = 0.62x + 31.79\n", "拟合优度 (R^2): 0.96\n", "被剔除的离群值 x:\n", "Empty DataFrame\n", "Columns: [x, y]\n", "Index: []\n", "被剔除的离群值 y:\n", "Empty DataFrame\n", "Columns: [x, y]\n", "Index: []\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "from scipy import stats\n", "import matplotlib.pyplot as plt\n", "import matplotlib.patches as patches\n", "\n", "# 示例数据，替换为你的实际数据\n", "df = result.copy()\n", "df.columns = ['x', 'y']\n", "\n", "# 计算均值和标准差\n", "mean_x = df['x'].mean()\n", "std_dev_x = df['x'].std()\n", "mean_y = df['y'].mean()\n", "std_dev_y = df['y'].std()\n", "\n", "# 定义阈值范围\n", "threshold_x = 3 * std_dev_x\n", "threshold_y = 3 * std_dev_y\n", "\n", "# 应用3σ原则，剔除离群值\n", "df_filtered = df[(np.abs(df['x'] - mean_x) <= threshold_x) & (np.abs(df['y'] - mean_y) <= threshold_y)]\n", "\n", "# 使用过滤后的数据进行最小二乘法拟合\n", "x_filtered = df_filtered['x'].values\n", "y_filtered = df_filtered['y'].values\n", "\n", "# 检查过滤后的数据点数量\n", "print(f\"Filtered data points: {len(x_filtered)}\")\n", "\n", "if len(x_filtered) < 2:\n", "    print(\"Not enough data points after filtering to perform linear regression.\")\n", "else:\n", "    slope, intercept, r_value, p_value, std_err = stats.linregress(x_filtered, y_filtered)\n", "    y_pred = intercept + slope * x_filtered\n", "\n", "    # 绘制原始数据和拟合直线\n", "    fig, ax = plt.subplots()\n", "\n", "     # 示例圆心和半径列表\n", "    for o, r in list_r_o:\n", "        circle = patches.Arc((o, 0), 2*r, 2*r, theta1=0, theta2=180, edgecolor='blue')\n", "        ax.add_patch(circle)\n", "\n", "    ax.scatter(x_filtered, y_filtered, color='blue', label='Filtered Data')  # 绘制过滤后的数据散点图\n", "    ax.plot(x_filtered, y_pred, color='red', label='Fitted line')  # 绘制拟合直线\n", "    \n", "    ax.set_xlim(min(x_filtered) - 1, max(x_filtered) + 1)\n", "    ax.set_ylim(0, max(y_filtered) + 1)\n", "    ax.set_aspect('equal')\n", "\n", "    plt.xlabel('X')\n", "    plt.ylabel('Y')\n", "    plt.title('Linear Regression Fit with <PERSON><PERSON> Removal (3σ Rule)')\n", "    plt.legend()\n", "    plt.grid(True)\n", "\n", "    # 在图中添加拟合直线的方程\n", "    equation = f'y = {slope:.2f}x + {intercept:.2f}'\n", "    plt.text(0.05, 0.95, equation, transform=ax.transAxes, fontsize=12, verticalalignment='top')\n", "\n", "    plt.show()\n", "\n", "    # 计算拟合优度 (R^2)\n", "    r_squared = r_value**2\n", "    print(f'拟合直线的方程: y = {slope:.2f}x + {intercept:.2f}')\n", "    print(f'拟合优度 (R^2): {r_squared:.2f}')\n", "\n", "    # 输出剔除的离群值\n", "    outliers_x = df[~((np.abs(df['x'] - mean_x) <= threshold_x))]\n", "    outliers_y = df[~((np.abs(df['y'] - mean_y) <= threshold_y))]\n", "    print(f'被剔除的离群值 x:\\n{outliers_x}')\n", "    print(f'被剔除的离群值 y:\\n{outliers_y}')\n"]}, {"cell_type": "code", "execution_count": 141, "id": "9c7549a1", "metadata": {"ExecuteTime": {"end_time": "2024-06-30T02:01:26.913545Z", "start_time": "2024-06-30T02:01:26.750698Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Filtered data points: 6\n"]}, {"data": {"image/png": "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************************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["拟合直线的方程: y = 0.62x + 31.79\n", "拟合优度 (R^2): 0.96\n", "被剔除的离群值 x:\n", "Empty DataFrame\n", "Columns: [x, y]\n", "Index: []\n", "被剔除的离群值 y:\n", "Empty DataFrame\n", "Columns: [x, y]\n", "Index: []\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "from scipy import stats\n", "import matplotlib.pyplot as plt\n", "import matplotlib.patches as patches\n", "\n", "# 示例数据，替换为你的实际数据\n", "df = result.copy()\n", "df.columns = ['x', 'y']\n", "\n", "# 计算均值和标准差\n", "mean_x = df['x'].mean()\n", "std_dev_x = df['x'].std()\n", "mean_y = df['y'].mean()\n", "std_dev_y = df['y'].std()\n", "\n", "# 定义阈值范围\n", "threshold_x = 3 * std_dev_x\n", "threshold_y = 3 * std_dev_y\n", "\n", "# 应用3σ原则，剔除离群值\n", "df_filtered = df[(np.abs(df['x'] - mean_x) <= threshold_x) & (np.abs(df['y'] - mean_y) <= threshold_y)]\n", "\n", "# 使用过滤后的数据进行最小二乘法拟合\n", "x_filtered = df_filtered['x'].values\n", "y_filtered = df_filtered['y'].values\n", "\n", "# 检查过滤后的数据点数量\n", "print(f\"Filtered data points: {len(x_filtered)}\")\n", "\n", "if len(x_filtered) < 2:\n", "    print(\"Not enough data points after filtering to perform linear regression.\")\n", "else:\n", "    slope, intercept, r_value, p_value, std_err = stats.linregress(x_filtered, y_filtered)\n", "    y_pred = intercept + slope * x_filtered\n", "\n", "    # 绘制原始数据和拟合直线\n", "    fig, ax = plt.subplots()\n", "\n", "\n", "    max_r = 0\n", "    max_o = 0\n", "    for o, r in list_r_o:\n", "        circle = patches.Arc((o, 0), 2*r, 2*r, theta1=0, theta2=180, edgecolor='blue')\n", "        ax.add_patch(circle)\n", "\n", "        if o > max_o:\n", "            max_o = o\n", "            max_r = r\n", "    \n", "    x_fit = np.linspace(0, max_o + max_r + 20, 100)\n", "    y_fit = intercept + slope * x_fit\n", "    ax.scatter(x_filtered, y_filtered, color='blue', label='Filtered Data')  # 绘制过滤后的数据散点图\n", "    ax.plot(x_fit, y_fit, color='red', label='Fitted line')  # 绘制拟合直线\n", "    \n", "    ax.set_xlim(-1, max_o + max_r + 20)\n", "    ax.set_ylim(0, y_fit.max())\n", "    ax.set_aspect('equal')\n", "\n", "    plt.xlabel('X')\n", "    plt.ylabel('Y')\n", "    plt.title('Linear Regression Fit with <PERSON><PERSON> Removal (3σ Rule)')\n", "    plt.legend()\n", "    plt.grid(True)\n", "\n", "    # 在图中添加拟合直线的方程\n", "    equation = f'y = {slope:.2f}x + {intercept:.2f}'\n", "    plt.text(0.05, 0.95, equation, transform=ax.transAxes, fontsize=12, verticalalignment='top')\n", "\n", "    plt.show()\n", "\n", "    # 计算拟合优度 (R^2)\n", "    r_squared = r_value**2\n", "    print(f'拟合直线的方程: y = {slope:.2f}x + {intercept:.2f}')\n", "    print(f'拟合优度 (R^2): {r_squared:.2f}')\n", "\n", "    # 输出剔除的离群值\n", "    outliers_x = df[~((np.abs(df['x'] - mean_x) <= threshold_x))]\n", "    outliers_y = df[~((np.abs(df['y'] - mean_y) <= threshold_y))]\n", "    print(f'被剔除的离群值 x:\\n{outliers_x}')\n", "    print(f'被剔除的离群值 y:\\n{outliers_y}')\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 5}