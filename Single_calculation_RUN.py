import sys
import numpy as np
from Single_calculation import *
from PyQt6.QtWidgets import QApplication, QMainWindow


class single_calculationWindow(QMainWindow, Ui_Form):
    def __init__(self, parent=None):
        super(single_calculationWindow, self).__init__(parent)
        self.setupUi(self)
        self.pushButton.clicked.connect(self.caculate)

    def caculate(self):
        self.σ3_a = float(self.lineEdit_15.text())
        self.σ1_a = float(self.lineEdit_16.text())

        self.σ3_b = float(self.lineEdit_17.text())
        self.σ1_b = float(self.lineEdit_18.text())

        self.φ = np.arcsin(
            (self.σ1_b + self.σ3_a - self.σ3_b - self.σ1_a) / (self.σ1_b + self.σ3_b - self.σ1_a - self.σ3_a))
        self.C = (((self.σ1_b - self.σ3_b) * 0.5) / np.sin(self.φ) - (self.σ1_b + self.σ3_b) * 0.5) * np.tan(self.φ)

        self.lineEdit_19.setText(str(round(np.degrees(self.φ), 2)))
        self.lineEdit_20.setText(str(round(self.C, 2)))


if __name__ == "__main__":
    app = QApplication(sys.argv)
    my_win = single_calculationWindow()
    my_win.show()
    sys.exit(app.exec())
