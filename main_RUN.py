import sys
from PyQt6.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QMainWindow, QFileDialog, QGraphicsPixmapItem, QGraphicsScene
from main import *
from Single_calculation_RUN import single_calculationWindow
from Multiple_computation_RUN import Multiple_computationWindow


class MyMainWindow(QMainWindow, Ui_MainWindow):
    def __init__(self, parent=None):
        super(MyMainWindow, self).__init__(parent)
        self.path = ''
        self.setupUi(self)
        self.pushButton.clicked.connect(self.open_single_calculation_window)
        self.pushButton_2.clicked.connect(self.open_Multiple_computation_window)
        # self.pushButton_3.clicked.connect(self.open_multicomponent_window)

    def open_single_calculation_window(self):
        self.encode_window = single_calculationWindow()
        self.encode_window.show()

    #
    def open_Multiple_computation_window(self):
        self.encode_window = Multiple_computationWindow()
        self.encode_window.show()
    #
    # def open_multicomponent_window(self):
    #     self.encode_window = multicomponentWindow()
    #     self.encode_window.show()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    my_win = MyMainWindow()
    my_win.show()
    sys.exit(app.exec())
