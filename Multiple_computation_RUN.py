import sys
import numpy as np
import pandas as pd
import statistics
import matplotlib.pyplot as plt
from scipy import stats
from Multiple_computation import *
from image_window import ImageWindow
from PyQt6.QtWidgets import QApplication, QMainWindow, QFileDialog


class Multiple_computationWindow(QMainWindow, Ui_Form):
    def __init__(self, parent=None):
        super(Multiple_computationWindow, self).__init__(parent)
        self.setupUi(self)
        self.image_windows = []
        self.pushButton.clicked.connect(self.file_input)
        self.pushButton_2.clicked.connect(self.caculate)
        self.pushButton_3.clicked.connect(self.fig_plot)

    def file_input(self):
        self.fileName, filetype = QFileDialog.getOpenFileName(self, "请选择实验数据：", '.', "实验数据 (*.xlsx)")
        while self.fileName == '':
            print('未选择数据，请重新选择：')
            self.fileName, filetype = QFileDialog.getOpenFileName(self, "请选择实验数据：", '.', "实验数据 (*.xlsx)")

    def caculate(self):
        data = pd.read_excel(self.fileName)
        print(data)
        list_σ = []
        list_C = []
        self.list_r_o = []
        for i in range(data.shape[0]):
            σ1_a = data.at[i, "强度"]
            σ3_a = data.at[i, "围压"]
            r_i = (σ1_a - σ3_a) / 2
            o_i = (σ1_a + σ3_a) / 2
            self.list_r_o.append([o_i, r_i])
            list_σ_temp = []
            list_C_temp = []
            for j in range(data.shape[0]):
                if i != j:
                    σ1_b = data.at[j, "强度"]
                    σ3_b = data.at[j, "围压"]
                    r_k = (σ1_b - σ3_b) / 2
                    o_k = (σ1_b + σ3_b) / 2

                    # 检查除零错误
                    if o_k - o_i == 0:
                        print(f"警告：除零错误，o_k - o_i = 0，跳过计算")
                        continue

                    σ_i = o_i - r_i * ((r_k - r_i) / (o_k - o_i))
                    # 检查开平方根的值是否为正数，避免无效值
                    sqrt_value = r_i ** 2 - (σ_i - o_i) ** 2
                    if sqrt_value >= 0:
                        τ_i = np.sqrt(sqrt_value)
                        list_σ_temp.append(round(σ_i, 2))
                        list_C_temp.append(round(τ_i, 2))
                    else:
                        # 如果开平方根的值为负数，跳过这个计算
                        print(f"警告：跳过无效计算，sqrt_value = {sqrt_value}")
                        continue
            list_σ.append(list_σ_temp)
            list_C.append(list_C_temp)

        medians = []
        corresponding_values = []

        for sublist1, sublist2 in zip(list_σ, list_C):
            if len(sublist1) == 0:
                print("警告：子列表为空，跳过")
                continue

            median_value = statistics.median(sublist1)

            # 找到最接近中位数的值的索引
            # 因为median可能不是列表中的实际值（偶数个元素时是平均值）
            closest_value = min(sublist1, key=lambda x: abs(x - median_value))
            median_index = sublist1.index(closest_value)

            corresponding_value = sublist2[median_index]
            medians.append(closest_value)  # 使用实际的最接近中位数的值
            corresponding_values.append(corresponding_value)

        print(f'每个子列表的中位数是: {medians}')
        print(f'对应的另一个子列表的值是: {corresponding_values}')

        result = pd.DataFrame([medians, corresponding_values]).T
        result.columns = ['σ', 'τ']

        # 获取离群点剔除比例
        try:
            outlier_percentage = float(self.lineEdit_21.text())
        except:
            outlier_percentage = 0.0

        if outlier_percentage > 0:
            # 基于百分比剔除离群点
            # 计算每个点到数据中心的马氏距离
            try:
                from scipy.spatial.distance import mahalanobis

                # 计算协方差矩阵
                cov_matrix = np.cov(result[['σ', 'τ']].T)
                mean_point = result[['σ', 'τ']].mean().values

                # 计算每个点的马氏距离
                distances = []
                for i in range(len(result)):
                    point = result.iloc[i][['σ', 'τ']].values
                    try:
                        dist = mahalanobis(point, mean_point, np.linalg.inv(cov_matrix))
                        distances.append(dist)
                    except:
                        distances.append(0)
            except ImportError:
                # 如果没有scipy，使用欧几里得距离
                print("警告：未安装scipy，使用欧几里得距离代替马氏距离")
                mean_point = result[['σ', 'τ']].mean().values
                distances = []
                for i in range(len(result)):
                    point = result.iloc[i][['σ', 'τ']].values
                    dist = np.sqrt(np.sum((point - mean_point) ** 2))
                    distances.append(dist)

            result['distance'] = distances

            # 按距离排序，剔除指定百分比的离群点
            n_outliers = int(len(result) * outlier_percentage / 100)
            result_sorted = result.sort_values('distance')
            df_filtered = result_sorted.iloc[:-n_outliers] if n_outliers > 0 else result_sorted
            df_filtered = df_filtered[['σ', 'τ']]  # 移除距离列
        else:
            # 使用3σ原则
            mean_x = result['σ'].mean()
            std_dev_x = result['σ'].std()
            mean_y = result['τ'].mean()
            std_dev_y = result['τ'].std()
            threshold_x = 3 * std_dev_x
            threshold_y = 3 * std_dev_y
            df_filtered = result[
                (np.abs(result['σ'] - mean_x) <= threshold_x) & (np.abs(result['τ'] - mean_y) <= threshold_y)]

        # 使用过滤后的数据进行最小二乘法拟合
        self.x_filtered = df_filtered['σ'].values
        self.y_filtered = df_filtered['τ'].values
        self.slope, self.intercept, r_value, p_value, std_err = stats.linregress(self.x_filtered, self.y_filtered)
        self.y_pred = self.intercept + self.slope * self.x_filtered

        # 计算拟合优度 (R^2)
        r_squared = r_value ** 2
        self.lineEdit_19.setText(f'y = {self.slope:.2f}x + {self.intercept:.2f}')
        self.lineEdit_20.setText(f'{r_squared:.2f}')
        # print(f'拟合直线的方程: y = {slope:.2f}x + {intercept:.2f}')
        # print(f'拟合优度 (R^2): {r_squared:.2f}')

        # 输出剔除的离群值
        outliers_x = result[~((np.abs(result['σ'] - mean_x) <= threshold_x))]
        outliers_y = result[~((np.abs(result['τ'] - mean_y) <= threshold_y))]
        print(f'被剔除的离群值 x:\n{outliers_x}')
        print(f'被剔除的离群值 y:\n{outliers_y}')

    def fig_plot(self):
        # 检查是否有有效数据
        if not hasattr(self, 'x_filtered') or len(self.x_filtered) == 0:
            print("错误：没有有效的数据进行绘图，请先进行计算")
            return

        # 绘制原始数据和拟合直线
        import matplotlib.patches as patches
        import matplotlib.colors as mcolors
        config = {
            "font.family": 'serif',
            "font.size": 12,
            "mathtext.fontset": 'stix',
            "font.serif": ['SimSun'],
        }
        plt.rcParams.update(config)

        # 设置纵横比为4:5
        fig, ax = plt.subplots(figsize=(10, 8))

        # 获取围压数据并创建颜色映射
        data = pd.read_excel(self.fileName)
        confining_pressures = sorted(data['围压'].unique())

        # 彩虹颜色序列
        rainbow_colors = ['red', 'orange', 'yellow', 'green', 'cyan', 'blue', 'purple', 'magenta']
        color_map = {}
        for i, pressure in enumerate(confining_pressures):
            color_map[pressure] = rainbow_colors[i % len(rainbow_colors)]

        # 绘制莫尔圆，按围压分组着色
        max_r = 0
        max_o = 0
        min_o = float('inf')

        for i, (o, r) in enumerate(self.list_r_o):
            # 获取对应的围压
            pressure = data.at[i, '围压']
            color = color_map[pressure]

            # 绘制完整的圆（上半圆）
            circle = patches.Arc((o, 0), 2 * r, 2 * r, theta1=0, theta2=180,
                               edgecolor=color, alpha=0.8, linewidth=2)
            ax.add_patch(circle)

            # 更新边界
            if o + r > max_o + max_r:
                max_o = o
                max_r = r
            if o - r < min_o:
                min_o = o - r

        # 计算显示范围，确保能容纳所有圆
        data_x_min = min(self.x_filtered.min(), min_o)
        data_x_max = max(self.x_filtered.max(), max_o + max_r)

        # 计算纵坐标最大值，确保能容纳所有圆的最高点
        circle_y_max = max_r  # 圆的最高点
        data_y_max = max(self.y_filtered.max(), circle_y_max)

        # 设置拟合直线的显示范围
        x_margin = (data_x_max - data_x_min) * 0.05  # 5%的边距
        x_fit_min = max(0, data_x_min - x_margin)
        x_fit_max = data_x_max + x_margin

        # 根据纵横比4:5调整显示范围
        x_range = x_fit_max - x_fit_min
        y_range = data_y_max * 1.1

        # 确保纵横比为4:5
        target_ratio = 4.0 / 5.0  # 高/宽
        current_ratio = y_range / x_range

        if current_ratio < target_ratio:
            # 需要增加y范围
            y_range = x_range * target_ratio
        else:
            # 需要增加x范围
            x_range = y_range / target_ratio
            x_center = (x_fit_min + x_fit_max) / 2
            x_fit_min = max(0, x_center - x_range / 2)
            x_fit_max = x_center + x_range / 2

        # 生成拟合直线
        x_fit = np.linspace(x_fit_min, x_fit_max, 100)
        y_fit = self.intercept + self.slope * x_fit

        # 只显示y值为正的部分
        positive_mask = y_fit >= 0
        x_fit = x_fit[positive_mask]
        y_fit = y_fit[positive_mask]

        # 绘制散点图和拟合直线
        ax.scatter(self.x_filtered, self.y_filtered, color='darkblue', s=60,
                  label='真实值', zorder=5, alpha=0.8, edgecolors='white', linewidth=0.5)
        ax.plot(x_fit, y_fit, color='red', linewidth=2.5, label='拟合直线', zorder=4)

        # 设置坐标轴范围，保持标准圆形
        ax.set_xlim(x_fit_min, x_fit_max)
        ax.set_ylim(0, y_range)
        ax.set_aspect('equal')  # 确保圆是标准圆形

        # 设置标签和标题
        plt.xlabel('σ (MPa)', fontsize=16)
        plt.ylabel('τ (MPa)', fontsize=16)
        plt.title('莫尔-库仑强度准则拟合', fontsize=18, pad=20)

        # 创建围压图例
        legend_elements = []
        for pressure in confining_pressures:
            color = color_map[pressure]
            legend_elements.append(plt.Line2D([0], [0], color=color, lw=3,
                                            label=f'围压 {pressure} MPa'))

        # 添加拟合数据图例
        legend_elements.append(plt.Line2D([0], [0], marker='o', color='w',
                                        markerfacecolor='darkblue', markersize=8,
                                        label='真实值', linestyle='None'))
        legend_elements.append(plt.Line2D([0], [0], color='red', lw=2.5,
                                        label='拟合直线'))

        # 图例位置保持在左上角
        plt.legend(handles=legend_elements, loc='upper left', fontsize=10, framealpha=0.9)

        # 设置网格
        plt.grid(True, alpha=0.3, linestyle='--')

        # 拟合方程显示在右边
        equation = f'y = {self.slope:.3f}x + {self.intercept:.3f}'
        r_squared = self.lineEdit_20.text()
        text_content = f'{equation}\nR² = {r_squared}'
        plt.text(0.98, 0.98, text_content, transform=plt.gca().transAxes,
                fontsize=12, verticalalignment='top', horizontalalignment='right',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.8))

        # 调整布局
        plt.tight_layout()

        window = ImageWindow(fig)  # 创建并显示独立窗口
        self.image_windows.append(window)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    my_win = Multiple_computationWindow()
    my_win.show()
    sys.exit(app.exec())
