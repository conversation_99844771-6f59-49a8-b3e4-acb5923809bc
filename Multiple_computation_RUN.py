import sys
import numpy as np
import pandas as pd
import statistics
import matplotlib.pyplot as plt
from scipy import stats
from Multiple_computation import *
from image_window import ImageWindow
from PyQt6.QtWidgets import QApplication, QMainWindow, QFileDialog


class Multiple_computationWindow(QMainWindow, Ui_Form):
    def __init__(self, parent=None):
        super(Multiple_computationWindow, self).__init__(parent)
        self.setupUi(self)
        self.image_windows = []
        self.pushButton.clicked.connect(self.file_input)
        self.pushButton_2.clicked.connect(self.caculate)
        self.pushButton_3.clicked.connect(self.fig_plot)

    def file_input(self):
        self.fileName, filetype = QFileDialog.getOpenFileName(self, "请选择实验数据：", '.', "实验数据 (*.xlsx)")
        while self.fileName == '':
            print('未选择数据，请重新选择：')
            self.fileName, filetype = QFileDialog.getOpenFileName(self, "请选择实验数据：", '.', "实验数据 (*.xlsx)")

    def caculate(self):
        data = pd.read_excel(self.fileName)
        print(data)
        list_σ = []
        list_C = []
        self.list_r_o = []
        for i in range(data.shape[0]):
            σ1_a = data.at[i, "强度"]
            σ3_a = data.at[i, "围压"]
            r_i = (σ1_a - σ3_a) / 2
            o_i = (σ1_a + σ3_a) / 2
            self.list_r_o.append([o_i, r_i])
            list_σ_temp = []
            list_C_temp = []
            for j in range(data.shape[0]):
                if i != j:
                    σ1_b = data.at[j, "强度"]
                    σ3_b = data.at[j, "围压"]
                    r_k = (σ1_b - σ3_b) / 2
                    o_k = (σ1_b + σ3_b) / 2

                    # 检查除零错误
                    if o_k - o_i == 0:
                        print(f"警告：除零错误，o_k - o_i = 0，跳过计算")
                        continue

                    σ_i = o_i - r_i * ((r_k - r_i) / (o_k - o_i))
                    # 检查开平方根的值是否为正数，避免无效值
                    sqrt_value = r_i ** 2 - (σ_i - o_i) ** 2
                    if sqrt_value >= 0:
                        τ_i = np.sqrt(sqrt_value)
                        list_σ_temp.append(round(σ_i, 2))
                        list_C_temp.append(round(τ_i, 2))
                    else:
                        # 如果开平方根的值为负数，跳过这个计算
                        print(f"警告：跳过无效计算，sqrt_value = {sqrt_value}")
                        continue
            list_σ.append(list_σ_temp)
            list_C.append(list_C_temp)

        medians = []
        corresponding_values = []

        for sublist1, sublist2 in zip(list_σ, list_C):
            if len(sublist1) == 0:
                print("警告：子列表为空，跳过")
                continue

            median_value = statistics.median(sublist1)

            # 找到最接近中位数的值的索引
            # 因为median可能不是列表中的实际值（偶数个元素时是平均值）
            closest_value = min(sublist1, key=lambda x: abs(x - median_value))
            median_index = sublist1.index(closest_value)

            corresponding_value = sublist2[median_index]
            medians.append(closest_value)  # 使用实际的最接近中位数的值
            corresponding_values.append(corresponding_value)

        print(f'每个子列表的中位数是: {medians}')
        print(f'对应的另一个子列表的值是: {corresponding_values}')

        result = pd.DataFrame([medians, corresponding_values]).T
        result.columns = ['σ', 'τ']
        # 计算均值和标准差
        mean_x = result['σ'].mean()
        std_dev_x = result['σ'].std()
        mean_y = result['τ'].mean()
        std_dev_y = result['τ'].std()

        # 定义阈值范围
        threshold_x = 3 * std_dev_x
        threshold_y = 3 * std_dev_y

        # 应用3σ原则，剔除离群值
        df_filtered = result[
            (np.abs(result['σ'] - mean_x) <= threshold_x) & (np.abs(result['τ'] - mean_y) <= threshold_y)]

        # 使用过滤后的数据进行最小二乘法拟合
        self.x_filtered = df_filtered['σ'].values
        self.y_filtered = df_filtered['τ'].values
        self.slope, self.intercept, r_value, p_value, std_err = stats.linregress(self.x_filtered, self.y_filtered)
        self.y_pred = self.intercept + self.slope * self.x_filtered

        # 计算拟合优度 (R^2)
        r_squared = r_value ** 2
        self.lineEdit_19.setText(f'y = {self.slope:.2f}x + {self.intercept:.2f}')
        self.lineEdit_20.setText(f'{r_squared:.2f}')
        # print(f'拟合直线的方程: y = {slope:.2f}x + {intercept:.2f}')
        # print(f'拟合优度 (R^2): {r_squared:.2f}')

        # 输出剔除的离群值
        outliers_x = result[~((np.abs(result['σ'] - mean_x) <= threshold_x))]
        outliers_y = result[~((np.abs(result['τ'] - mean_y) <= threshold_y))]
        print(f'被剔除的离群值 x:\n{outliers_x}')
        print(f'被剔除的离群值 y:\n{outliers_y}')

    def fig_plot(self):
        # 检查是否有有效数据
        if not hasattr(self, 'x_filtered') or len(self.x_filtered) == 0:
            print("错误：没有有效的数据进行绘图，请先进行计算")
            return

        # 绘制原始数据和拟合直线
        import matplotlib.patches as patches
        config = {
            "font.family": 'serif',
            "font.size": 12,
            "mathtext.fontset": 'stix',
            "font.serif": ['SimSun'],
        }
        plt.rcParams.update(config)
        fig, ax = plt.subplots(figsize=(10, 8))

        # 绘制莫尔圆
        max_r = 0
        max_o = 0
        min_o = float('inf')
        for o, r in self.list_r_o:
            circle = patches.Arc((o, 0), 2 * r, 2 * r, theta1=0, theta2=180,
                               edgecolor='lightblue', alpha=0.7, linewidth=1.5)
            ax.add_patch(circle)
            if o + r > max_o + max_r:
                max_o = o
                max_r = r
            if o - r < min_o:
                min_o = o - r

        # 计算合理的显示范围
        data_x_min = min(self.x_filtered.min(), min_o)
        data_x_max = max(self.x_filtered.max(), max_o + max_r)
        data_y_max = self.y_filtered.max()

        # 设置拟合直线的显示范围，只在数据范围内显示
        x_margin = (data_x_max - data_x_min) * 0.1  # 10%的边距
        x_fit_min = max(0, data_x_min - x_margin)
        x_fit_max = data_x_max + x_margin

        # 生成拟合直线的x值，限制在合理范围内
        x_fit = np.linspace(x_fit_min, x_fit_max, 100)
        y_fit = self.intercept + self.slope * x_fit

        # 只显示y值为正的部分
        positive_mask = y_fit >= 0
        x_fit = x_fit[positive_mask]
        y_fit = y_fit[positive_mask]

        # 绘制散点图和拟合直线
        ax.scatter(self.x_filtered, self.y_filtered, color='darkblue', s=60,
                  label='真实值', zorder=5, alpha=0.8, edgecolors='white', linewidth=0.5)
        ax.plot(x_fit, y_fit, color='red', linewidth=2.5, label='拟合直线', zorder=4)

        # 设置坐标轴范围
        ax.set_xlim(x_fit_min, x_fit_max)
        ax.set_ylim(0, max(data_y_max * 1.1, y_fit.max() * 1.1))

        # 不强制等比例，让图形更好地利用空间
        # ax.set_aspect('equal')  # 注释掉这行

        # 设置标签和标题
        plt.xlabel('σ (MPa)', fontsize=16)
        plt.ylabel('τ (MPa)', fontsize=16)
        plt.title('莫尔-库仑强度准则拟合', fontsize=18, pad=20)

        # 优化图例位置
        plt.legend(loc='upper left', fontsize=12, framealpha=0.9)

        # 设置网格
        plt.grid(True, alpha=0.3, linestyle='--')

        # 优化拟合方程的显示位置
        equation = f'y = {self.slope:.3f}x + {self.intercept:.3f}'
        r_squared = self.lineEdit_20.text()
        text_content = f'{equation}\nR² = {r_squared}'
        plt.text(0.02, 0.98, text_content, transform=plt.gca().transAxes,
                fontsize=12, verticalalignment='top', horizontalalignment='left',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.8))

        # 调整布局
        plt.tight_layout()

        window = ImageWindow(fig)  # 创建并显示独立窗口
        self.image_windows.append(window)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    my_win = Multiple_computationWindow()
    my_win.show()
    sys.exit(app.exec())
