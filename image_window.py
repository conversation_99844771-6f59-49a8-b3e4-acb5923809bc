from PyQt6.QtWidgets import QMain<PERSON><PERSON>ow, QVBoxLayout, QWidget
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg
class ImageWindow(QMainWindow):
    def __init__(self, fig, parent=None):
        super(ImageWindow, self).__init__(parent)
        self.initUI(fig)
    def initUI(self, fig):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        canvas = FigureCanvasQTAgg(fig)
        layout.addWidget(canvas)
        self.setCentralWidget(widget)
        self.setWindowTitle("Image Window")
        self.show()