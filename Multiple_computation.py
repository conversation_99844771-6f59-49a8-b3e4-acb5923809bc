from PyQt6 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(709, 350)
        self.groupBox_4 = QtWidgets.QGroupBox(parent=Form)
        self.groupBox_4.setGeometry(QtCore.QRect(10, 20, 381, 291))
        font = QtGui.QFont()
        font.setPointSize(18)
        self.groupBox_4.setFont(font)
        self.groupBox_4.setObjectName("groupBox_4")
        self.lineEdit_19 = QtWidgets.QLineEdit(parent=self.groupBox_4)
        self.lineEdit_19.setGeometry(QtCore.QRect(170, 70, 201, 31))
        font = QtGui.QFont()
        font.setPointSize(15)
        self.lineEdit_19.setFont(font)
        self.lineEdit_19.setObjectName("lineEdit_19")
        self.lineEdit_20 = QtWidgets.QLineEdit(parent=self.groupBox_4)
        self.lineEdit_20.setGeometry(QtCore.QRect(170, 140, 91, 31))
        self.lineEdit_20.setObjectName("lineEdit_20")
        self.label_18 = QtWidgets.QLabel(parent=self.groupBox_4)
        self.label_18.setGeometry(QtCore.QRect(30, 70, 181, 31))
        font = QtGui.QFont()
        font.setPointSize(15)
        self.label_18.setFont(font)
        self.label_18.setObjectName("label_18")
        self.label_19 = QtWidgets.QLabel(parent=self.groupBox_4)
        self.label_19.setGeometry(QtCore.QRect(30, 140, 181, 31))
        font = QtGui.QFont()
        font.setPointSize(15)
        self.label_19.setFont(font)
        self.label_19.setObjectName("label_19")
        self.label_20 = QtWidgets.QLabel(parent=self.groupBox_4)
        self.label_20.setGeometry(QtCore.QRect(30, 190, 181, 31))
        font = QtGui.QFont()
        font.setPointSize(15)
        self.label_20.setFont(font)
        self.label_20.setObjectName("label_20")
        self.lineEdit_21 = QtWidgets.QLineEdit(parent=self.groupBox_4)
        self.lineEdit_21.setGeometry(QtCore.QRect(170, 190, 91, 31))
        self.lineEdit_21.setText("0")
        self.lineEdit_21.setObjectName("lineEdit_21")
        self.layoutWidget = QtWidgets.QWidget(parent=Form)
        self.layoutWidget.setGeometry(QtCore.QRect(450, 40, 231, 221))
        self.layoutWidget.setObjectName("layoutWidget")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.layoutWidget)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.pushButton = QtWidgets.QPushButton(parent=self.layoutWidget)
        font = QtGui.QFont()
        font.setPointSize(20)
        self.pushButton.setFont(font)
        self.pushButton.setObjectName("pushButton")
        self.verticalLayout.addWidget(self.pushButton)
        self.pushButton_2 = QtWidgets.QPushButton(parent=self.layoutWidget)
        font = QtGui.QFont()
        font.setPointSize(20)
        self.pushButton_2.setFont(font)
        self.pushButton_2.setObjectName("pushButton_2")
        self.verticalLayout.addWidget(self.pushButton_2)
        self.pushButton_3 = QtWidgets.QPushButton(parent=self.layoutWidget)
        font = QtGui.QFont()
        font.setPointSize(20)
        self.pushButton_3.setFont(font)
        self.pushButton_3.setObjectName("pushButton_3")
        self.verticalLayout.addWidget(self.pushButton_3)

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "多组实验拟合"))
        self.groupBox_4.setTitle(_translate("Form", "结果输出"))
        self.label_18.setText(_translate("Form", "拟合直线方程："))
        self.label_19.setText(_translate("Form", "拟合优度："))
        self.label_20.setText(_translate("Form", "离群点剔除比例(%)："))
        self.pushButton.setText(_translate("Form", "实验数据导入"))
        self.pushButton_2.setText(_translate("Form", "结果计算"))
        self.pushButton_3.setText(_translate("Form", "图像绘制"))
